<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;

class SettingsController extends Controller
{
    /**
     * Display the vendor settings page
     */
    public function index()
    {
        $vendor = Auth::user()->vendor;
        
        return view('vendor.settings.index', compact('vendor'));
    }
    
    /**
     * Update vendor settings
     */
    public function update(Request $request)
    {
        $vendor = Auth::user()->vendor;
        
        $request->validate([
            'business_name' => 'required|string|max:255|unique:vendors,business_name,' . $vendor->id,
            'description' => 'nullable|string|max:1000',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'banner' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:4096',
            'contact_phone' => 'required|string|max:20',
            'contact_email_business' => 'required|email|max:255',
            'address_line1' => 'required|string|max:255',
            'address_line2' => 'nullable|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'bank_name' => 'nullable|string|max:100',
            'account_name' => 'nullable|string|max:255',
            'account_number' => 'nullable|string|max:20',
            'business_registration_number' => 'nullable|string|max:50',
            'tax_identification_number' => 'nullable|string|max:50',
            'delivery_policy_text' => 'nullable|string|max:2000',
        ]);
        
        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($vendor->logo) {
                Storage::delete($vendor->logo);
            }
            
            $logoPath = $request->file('logo')->store('vendors/logos', 'public');
            $vendor->logo = $logoPath;
        }
        
        // Handle banner upload
        if ($request->hasFile('banner')) {
            // Delete old banner if exists
            if ($vendor->banner) {
                Storage::delete($vendor->banner);
            }
            
            $bannerPath = $request->file('banner')->store('vendors/banners', 'public');
            $vendor->banner = $bannerPath;
        }
        
        // Update bank account details
        $bankDetails = [];
        if ($request->filled('bank_name')) {
            $bankDetails = [
                'bank_name' => $request->bank_name,
                'account_name' => $request->account_name,
                'account_number' => $request->account_number,
            ];
        }
        
        // Update vendor details
        $vendor->update([
            'business_name' => $request->business_name,
            'description' => $request->description,
            'contact_phone' => $request->contact_phone,
            'contact_email_business' => $request->contact_email_business,
            'address_line1' => $request->address_line1,
            'address_line2' => $request->address_line2,
            'city' => $request->city,
            'state' => $request->state,
            'postal_code' => $request->postal_code,
            'bank_account_details' => !empty($bankDetails) ? json_encode($bankDetails) : $vendor->bank_account_details,
            'business_registration_number' => $request->business_registration_number,
            'tax_identification_number' => $request->tax_identification_number,
            'delivery_policy_text' => $request->delivery_policy_text,
        ]);
        
        return redirect()->route('vendor.settings.index')
            ->with('success', 'Settings updated successfully.');
    }
}