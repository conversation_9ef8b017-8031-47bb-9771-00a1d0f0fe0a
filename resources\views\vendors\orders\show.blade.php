@extends('layouts.vendor')

@section('title', 'Order Details')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="flex items-center justify-between mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Order #{{ $order->order_number }}</h1>
                <p class="text-gray-600">Placed on {{ $order->created_at->format('M d, Y g:i A') }}</p>
            </div>
            <div class="text-right">
                <span class="px-3 py-1 text-sm rounded-full 
                    @if($order->status === 'delivered') bg-green-100 text-green-800
                    @elseif($order->status === 'shipped') bg-blue-100 text-blue-800
                    @elseif($order->status === 'processing') bg-yellow-100 text-yellow-800
                    @else bg-gray-100 text-gray-800 @endif">
                    {{ ucfirst($order->status) }}
                </span>
            </div>
        </div>

        <!-- Order Details Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Customer Information -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Customer Information</h2>
                <div class="space-y-3">
                    <div>
                        <p class="text-sm text-gray-500">Name</p>
                        <p class="font-medium">{{ $order->customer_name }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Email</p>
                        <p class="font-medium">{{ $order->customer_email }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Phone</p>
                        <p class="font-medium">{{ $order->customer_phone }}</p>
                    </div>
                </div>
            </div>

            <!-- Shipping Information -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Shipping Information</h2>
                <div class="space-y-3">
                    <div>
                        <p class="text-sm text-gray-500">Address</p>
                        <p class="font-medium">{{ $order->shipping_address }}</p>
                        <p class="font-medium">{{ $order->shipping_city }}, {{ $order->shipping_state }}</p>
                        <p class="font-medium">{{ $order->shipping_postal_code }}</p>
                    </div>
                    @if($order->delivery_notes)
                        <div>
                            <p class="text-sm text-gray-500">Delivery Notes</p>
                            <p class="font-medium">{{ $order->delivery_notes }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Order Items -->
        <div class="bg-white rounded-lg shadow-sm mt-6">
            <div class="p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Order Items</h2>
                <div class="divide-y divide-gray-200">
                    @foreach($order->orderItems as $item)
                        <div class="py-4 flex items-center">
                            <div class="flex-shrink-0 w-16 h-16">
                                @if($item->product->image)
                                    <img src="{{ $item->product->image }}" alt="{{ $item->product->name }}" class="w-full h-full object-cover rounded">
                                @else
                                    <div class="w-full h-full bg-gray-100 rounded flex items-center justify-center">
                                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                @endif
                            </div>
                            <div class="ml-4 flex-1">
                                <h3 class="text-sm font-medium text-gray-900">{{ $item->product->name }}</h3>
                                <p class="text-sm text-gray-500">Quantity: {{ $item->quantity }}</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-900">₦{{ number_format($item->price * $item->quantity) }}</p>
                                <p class="text-xs text-gray-500">₦{{ number_format($item->price) }} each</p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
            <div class="bg-gray-50 px-6 py-4">
                <div class="flex justify-between text-sm">
                    <span class="text-gray-600">Subtotal</span>
                    <span class="text-gray-900">₦{{ number_format($order->total) }}</span>
                </div>
            </div>
        </div>

        <!-- Shipping Status -->
        @if($order->shipment)
            <div class="bg-white rounded-lg shadow-sm mt-6 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Shipping Status</h2>
                <div class="mb-4">
                    <p class="text-sm text-gray-500">Tracking Number</p>
                    <p class="text-lg font-medium text-blue-600">{{ $order->tracking_number }}</p>
                </div>
                @include('shipments._timeline', ['timeline' => $order->shipment->timeline])
            </div>
        @elseif($order->isEligibleForShipping())
            <div class="bg-white rounded-lg shadow-sm mt-6 p-6">
                <div class="text-center">
                    <h2 class="text-lg font-semibold text-gray-900 mb-2">Create Shipment</h2>
                    <p class="text-gray-600 mb-4">This order is ready for shipping. Create a shipment to start the delivery process.</p>
                    <button onclick="createShipment('{{ $order->id }}')" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Create Shipment
                    </button>
                </div>
            </div>
        @endif
    </div>
</div>

@push('scripts')
<script>
    function createShipment(orderId) {
        if (confirm('Are you sure you want to create a shipment for this order?')) {
            fetch(`/vendor/orders/${orderId}/ship`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert('Error creating shipment: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error creating shipment. Please try again.');
            });
        }
    }
</script>
@endpush
@endsection
