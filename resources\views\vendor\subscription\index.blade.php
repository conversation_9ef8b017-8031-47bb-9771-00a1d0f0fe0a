@extends('layouts.vendor')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">Subscription Management</h2>
            
            <!-- Current Subscription Status -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-dark text-white">
                            <h5 class="mb-0">Current Subscription</h5>
                        </div>
                        <div class="card-body">
                            @if($vendor->subscription_status === 'active')
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="badge bg-success fs-6">Active</span>
                                    @if($vendor->subscription_expires_at)
                                        <span class="text-muted">
                                            Expires: {{ $vendor->subscription_expires_at->format('M d, Y') }}
                                        </span>
                                    @endif
                                </div>
                                
                                <p><strong>Plan:</strong> 
                                    @if($vendor->delivery_zone_type === 'platform_delivery_zone')
                                        Platform Delivery Zone (₦10,000/month)
                                    @else
                                        Other States Zone (₦7,000/month)
                                    @endif
                                </p>
                                
                                @if($vendor->subscription_expires_at)
                                    @php
                                        $daysRemaining = now()->diffInDays($vendor->subscription_expires_at, false);
                                    @endphp
                                    
                                    @if($daysRemaining <= 7 && $daysRemaining > 0)
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            Your subscription expires in {{ $daysRemaining }} days. 
                                            <a href="{{ route('vendor.subscription.pay') }}" class="alert-link">Renew now</a>
                                        </div>
                                    @elseif($daysRemaining <= 0)
                                        <div class="alert alert-danger">
                                            <i class="fas fa-times-circle me-2"></i>
                                            Your subscription has expired. 
                                            <a href="{{ route('vendor.subscription.pay') }}" class="alert-link">Renew now</a>
                                        </div>
                                    @endif
                                @endif
                                
                            @elseif($vendor->subscription_status === 'expired')
                                <div class="alert alert-danger">
                                    <h6 class="alert-heading">Subscription Expired</h6>
                                    <p class="mb-0">Your subscription has expired. Please renew to continue selling.</p>
                                </div>
                                <a href="{{ route('vendor.subscription.pay') }}" class="btn btn-danger">
                                    Renew Subscription
                                </a>
                                
                            @elseif($vendor->subscription_status === 'pending_payment')
                                <div class="alert alert-warning">
                                    <h6 class="alert-heading">Payment Required</h6>
                                    <p class="mb-0">Please complete your subscription payment to activate your vendor account.</p>
                                </div>
                                <a href="{{ route('vendor.subscription.pay') }}" class="btn btn-warning">
                                    Pay Subscription
                                </a>
                                
                            @else
                                <div class="alert alert-secondary">
                                    <p class="mb-0">No active subscription</p>
                                </div>
                                <a href="{{ route('vendor.subscription.pay') }}" class="btn btn-primary">
                                    Subscribe Now
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">Subscription Benefits</h5>
                        </div>
                        <div class="card-body">
                            @if($vendor->delivery_zone_type === 'platform_delivery_zone')
                                <h6>Platform Delivery Zone Benefits:</h6>
                                <ul>
                                    <li>Platform handles all deliveries</li>
                                    <li>Delivery on Monday, Wednesday, Friday</li>
                                    <li>Professional logistics management</li>
                                    <li>Real-time tracking for customers</li>
                                    <li>Reduced delivery hassles</li>
                                </ul>
                            @else
                                <h6>Other States Zone Benefits:</h6>
                                <ul>
                                    <li>Lower subscription fee</li>
                                    <li>Full control over delivery</li>
                                    <li>Direct customer relationships</li>
                                    <li>Flexible delivery schedules</li>
                                    <li>Custom delivery options</li>
                                </ul>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Payment History -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Payment History</h5>
                </div>
                <div class="card-body">
                    @if($vendor->subscription)
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Amount</th>
                                        <th>Plan</th>
                                        <th>Status</th>
                                        <th>Reference</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>{{ $vendor->last_subscription_payment_at ? $vendor->last_subscription_payment_at->format('M d, Y') : 'N/A' }}</td>
                                        <td>₦{{ number_format($vendor->subscription_fee, 2) }}</td>
                                        <td>{{ $vendor->subscription->plan_name ?? 'Monthly' }}</td>
                                        <td>
                                            <span class="badge bg-success">Paid</span>
                                        </td>
                                        <td>{{ $vendor->subscription->paystack_reference ?? 'N/A' }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted">No payment history available.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection