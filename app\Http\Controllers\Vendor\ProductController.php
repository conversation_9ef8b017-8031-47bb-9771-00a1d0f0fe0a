<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Color;
use App\Models\Size;
use App\Models\ProductVariant;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class ProductController extends Controller
{
    /**
     * Display a listing of the vendor's products.
     */
    public function index()
    {
        $products = auth()->user()->vendor->products()->latest()->paginate(10);
        return view('vendor.products.index', compact('products'));
    }

    /**
     * Show the form for creating a new product.
     */
    public function create()
    {
        $categories = Category::all();
        $brands = Brand::all();
        $colors = Color::orderBy('name')->get();
        $sizes = Size::orderBy('name')->get();
        return view('vendor.products.create', compact('categories', 'brands', 'colors', 'sizes'));
    }

    /**
     * Store a newly created product in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'brand_id' => 'nullable|exists:brands,id',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            // 'discount_price' => 'nullable|numeric|min:0|lt:price', // Base product discount, consider if variants have their own pricing structure
            'stock_quantity' => 'required|integer|min:0', // Base product stock, might be 0 if all stock is in variants
            'sku' => 'nullable|string|max:255|unique:products,sku', // Base product SKU
            'image' => 'nullable|image|max:2048', // Main product image
            'is_active' => 'boolean',

            'variants' => 'nullable|array',
            'variants.*.color_id' => 'nullable|exists:colors,id',
            'variants.*.size_id' => 'nullable|exists:sizes,id',
            'variants.*.sku' => 'nullable|string|max:255|unique:product_variants,sku', // SKU should be unique across all variants
            'variants.*.price_adjustment' => 'nullable|numeric',
            'variants.*.stock_quantity' => 'required_with:variants.*.color_id,variants.*.size_id|integer|min:0', // Required if a variant is defined
            'variants.*.image' => 'nullable|image|max:2048',
            // Custom rule for the whole variant entry to check color/size uniqueness within the submission
            'variants.*' => [
                function ($attribute, $variantData, $fail) use ($request) {
                    $currentIndex = explode('.', $attribute)[1];
                    $currentColorId = $variantData['color_id'] ?? null;
                    $currentSizeId = $variantData['size_id'] ?? null;

                    // Skip check if this variant row is essentially empty (no color, no size, no sku)
                    // This helps ignore blank 'template' rows if they were somehow submitted without data.
                    if (empty($currentColorId) && empty($currentSizeId) && empty($variantData['sku'])) {
                        return; 
                    }

                    // Check against other variants in the CURRENT SUBMISSION
                    foreach ($request->input('variants') as $key => $otherVariantData) {
                        if ($key == $currentIndex) continue; // Don't compare with self in submission array

                        // Skip comparison if the other variant row is effectively empty
                        if (empty($otherVariantData['color_id']) && empty($otherVariantData['size_id']) && empty($otherVariantData['sku'])) {
                            continue;
                        }

                        $otherColorId = $otherVariantData['color_id'] ?? null;
                        $otherSizeId = $otherVariantData['size_id'] ?? null;

                        if ($otherColorId == $currentColorId && $otherSizeId == $currentSizeId) {
                            // Provide a more user-friendly index (1-based)
                            $fail("Variant #" . (intval($currentIndex) + 1) . ": Duplicate color/size combination found with variant #" . (intval($key) + 1) . " in your submission.");
                            return; // Stop further checks for this variant if duplicate in submission
                        }
                    }
                }
            ]
        ]);

        $productData = [
            'name' => $validatedData['name'],
            'slug' => Str::slug($validatedData['name']) . '-' . uniqid(), // Ensure slug is unique
            'description' => $validatedData['description'],
            'price' => $validatedData['price'],
            'stock_quantity' => $validatedData['stock_quantity'],
            'sku' => $validatedData['sku'] ?? null,
            'category_id' => $validatedData['category_id'],
            'brand_id' => $validatedData['brand_id'] ?? null,
            'vendor_id' => auth()->user()->vendor->id,
            'is_active' => $request->has('is_active'),
        ];

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('product-images', 'public');
            $productData['image_url'] = $imagePath; // Storing relative path
        }

        $product = Product::create($productData);

        if ($request->has('variants')) {
            foreach ($request->input('variants') as $index => $variantData) {
                // Skip if not enough data to form a variant (e.g. no color and no size selected, and no SKU)
                if (empty($variantData['color_id']) && empty($variantData['size_id']) && empty($variantData['sku'])) {
                    continue;
                }

                $newVariantData = [
                    'product_id' => $product->id,
                    'color_id' => $variantData['color_id'] ?? null,
                    'size_id' => $variantData['size_id'] ?? null,
                    'sku' => $variantData['sku'] ?? null,
                    'price_adjustment' => $variantData['price_adjustment'] ?? null,
                    'stock_quantity' => $variantData['stock_quantity'] ?? 0,
                ];

                if ($request->hasFile("variants.{$index}.image")) {
                    $variantImagePath = $request->file("variants.{$index}.image")->store('variant-images', 'public');
                    $newVariantData['image_path'] = $variantImagePath;
                }
                ProductVariant::create($newVariantData);
            }
        }

        return redirect()->route('vendor.products.index')
            ->with('success', 'Product created successfully with variants.');
    }

    /**
     * Show the form for editing the specified product.
     */
    public function edit(Product $product)
    {
        // Ensure the vendor can only edit their own products
        if ($product->vendor_id !== auth()->user()->vendor->id) {
            abort(403, 'Unauthorized action.');
        }

        $categories = Category::all();
        $brands = Brand::all();
        $colors = Color::orderBy('name')->get();
        $sizes = Size::orderBy('name')->get();
        $product->load('variants.color', 'variants.size');
        return view('vendor.products.edit', compact('product', 'categories', 'brands', 'colors', 'sizes'));
    }

    /**
     * Update the specified product in storage.
     */
    public function update(Request $request, Product $product)
    {
        // Ensure the vendor can only update their own products
        if ($product->vendor_id !== auth()->user()->vendor->id) {
            abort(403, 'Unauthorized action.');
        }

        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'brand_id' => 'nullable|exists:brands,id',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'stock_quantity' => 'required|integer|min:0',
            'sku' => 'nullable|string|max:255|unique:products,sku,' . $product->id, // Ignore current product's SKU
            'image' => 'nullable|image|max:2048',
            'is_active' => 'boolean',

            'variants' => 'nullable|array',
            'variants.*.id' => 'nullable|exists:product_variants,id', // For existing variants
            'variants.*.color_id' => 'nullable|exists:colors,id',
            'variants.*.size_id' => 'nullable|exists:sizes,id',
            'variants.*.sku' => [
                'nullable',
                'string',
                'max:255',
                function ($attribute, $value, $fail) use ($request) { // Removed $product, not needed here as SKU is globally unique for variants
                    if (empty($value)) return; // Allow empty SKU
                    $index = explode('.', $attribute)[1];
                    $variantId = $request->input("variants.{$index}.id");
                    $query = ProductVariant::where('sku', $value);
                    if ($variantId) {
                        $query->where('id', '!=', $variantId);
                    }
                    if ($query->exists()) {
                        $fail("The SKU ('{$value}') for variant #" . ($index + 1) . " has already been taken.");
                    }
                }
            ],
            'variants.*.price_adjustment' => 'nullable|numeric',
            'variants.*.stock_quantity' => 'required_with:variants.*.color_id,variants.*.size_id|integer|min:0',
            'variants.*.image' => 'nullable|image|max:2048',
            // Custom rule for the whole variant entry to check color/size uniqueness
            'variants.*' => [ 
                function ($attribute, $variantData, $fail) use ($request, $product) {
                    $currentIndex = explode('.', $attribute)[1];
                    $currentVariantIdInForm = $variantData['id'] ?? null;
                    $currentColorId = $variantData['color_id'] ?? null;
                    $currentSizeId = $variantData['size_id'] ?? null;

                    // Skip check if this variant row is essentially empty (no color, no size, no sku, and no ID indicating it's an existing one to be cleared)
                    // This condition might need refinement based on how 'empty' rows are handled or intended to be submitted.
                    if (empty($currentColorId) && empty($currentSizeId) && empty($variantData['sku']) && empty($currentVariantIdInForm)) {
                         // If it's an existing variant being cleared (id is present, but color/size/sku are removed),
                         // this uniqueness check might not be relevant for it, as it might be deleted or become an 'empty' variant.
                         // However, the current deletion logic relies on ID presence, so an existing variant row submitted empty will likely be updated to empty.
                         // Let's assume for now that an 'active' variant needs this check.
                         // The outer loop already skips truly empty new rows.
                    }

                    // 1. Check against other variants in the CURRENT SUBMISSION
                    foreach ($request->input('variants') as $key => $otherVariantData) {
                        if ($key == $currentIndex) continue; // Don't compare with self in submission array

                        // Skip comparison if the other variant row is effectively empty and new
                        if (empty($otherVariantData['id']) && empty($otherVariantData['color_id']) && empty($otherVariantData['size_id']) && empty($otherVariantData['sku'])) {
                            continue;
                        }

                        $otherColorId = $otherVariantData['color_id'] ?? null;
                        $otherSizeId = $otherVariantData['size_id'] ?? null;

                        if ($otherColorId == $currentColorId && $otherSizeId == $currentSizeId) {
                            $fail("Variant #" . ($currentIndex + 1) . ": Duplicate color/size combination found with variant #" . ($key + 1) . " in your submission.");
                            return; // Stop further checks for this variant if duplicate in submission
                        }
                    }

                    // 2. Check against variants in the DATABASE for this product
                    $dbQuery = ProductVariant::where('product_id', $product->id)
                                             ->where('color_id', $currentColorId) // Handles null comparison correctly
                                             ->where('size_id', $currentSizeId);  // Handles null comparison correctly

                    if ($currentVariantIdInForm) { // If we are editing an existing variant
                        $dbQuery->where('id', '!=', $currentVariantIdInForm); // Exclude self from DB check
                    }

                    if ($dbQuery->exists()) {
                        $fail("Variant #" . ($currentIndex + 1) . ": This color/size combination already exists in the database for this product.");
                    }
                }
            ]
        ]);

        $productData = [
            'name' => $validatedData['name'],
            // Slug update strategy: only if name changes, or never, or always. For now, let's not update slug automatically on edit.
            // 'slug' => Str::slug($validatedData['name']) . '-' . uniqid(), 
            'description' => $validatedData['description'],
            'price' => $validatedData['price'],
            'stock_quantity' => $validatedData['stock_quantity'],
            'sku' => $validatedData['sku'] ?? null,
            'category_id' => $validatedData['category_id'],
            'brand_id' => $validatedData['brand_id'] ?? null,
            'is_active' => $request->has('is_active'),
        ];

        if ($request->hasFile('image')) {
            if ($product->image_url && Storage::disk('public')->exists($product->image_url)) {
                Storage::disk('public')->delete($product->image_url);
            }
            $imagePath = $request->file('image')->store('product-images', 'public');
            $productData['image_url'] = $imagePath;
        }

        $product->update($productData);

        $submittedVariantIds = [];

        if ($request->has('variants')) {
            foreach ($request->input('variants') as $index => $variantData) {
                // Skip if not enough data to form a variant (e.g. no color and no size selected, and no SKU)
                // This check might need refinement if we allow variants with only SKU for example
                if (empty($variantData['color_id']) && empty($variantData['size_id']) && empty($variantData['sku']) && empty($variantData['id'])) {
                    continue;
                }

                $variantDetails = [
                    'product_id' => $product->id,
                    'color_id' => $variantData['color_id'] ?? null,
                    'size_id' => $variantData['size_id'] ?? null,
                    'sku' => $variantData['sku'] ?? null,
                    'price_adjustment' => $variantData['price_adjustment'] ?? null,
                    'stock_quantity' => $variantData['stock_quantity'] ?? 0,
                ];

                $variant = null;
                if (!empty($variantData['id'])) {
                    $variant = ProductVariant::find($variantData['id']);
                    if ($variant && $variant->product_id !== $product->id) { // Ensure vendor owns this variant
                        abort(403, 'Unauthorized action on variant.');
                    }
                }

                if ($request->hasFile("variants.{$index}.image")) {
                    if ($variant && $variant->image_path && Storage::disk('public')->exists($variant->image_path)) {
                        Storage::disk('public')->delete($variant->image_path);
                    }
                    $variantImagePath = $request->file("variants.{$index}.image")->store('variant-images', 'public');
                    $variantDetails['image_path'] = $variantImagePath;
                } elseif (isset($variantData['current_image_path']) && $variant) { // Keep old image if no new one uploaded
                    $variantDetails['image_path'] = $variant->image_path; 
                }

                if ($variant) { // Existing variant: update
                    $variant->update($variantDetails);
                    $submittedVariantIds[] = $variant->id;
                } else { // New variant: create
                    // Ensure it's not a completely empty new row from the form that wasn't filled
                    if (!empty($variantDetails['color_id']) || !empty($variantDetails['size_id']) || !empty($variantDetails['sku'])) {
                        $newVariant = ProductVariant::create($variantDetails);
                        $submittedVariantIds[] = $newVariant->id;
                    }
                }
            }
        }

        // Delete variants that were not in the submission
        $existingVariantIds = $product->variants()->pluck('id')->all();
        $variantsToDelete = array_diff($existingVariantIds, $submittedVariantIds);
        if (!empty($variantsToDelete)) {
            $variants = ProductVariant::whereIn('id', $variantsToDelete)->get();
            foreach($variants as $variantToDel) {
                if ($variantToDel->image_path && Storage::disk('public')->exists($variantToDel->image_path)) {
                    Storage::disk('public')->delete($variantToDel->image_path);
                }
                $variantToDel->delete();
            }
        }

        return redirect()->route('vendor.products.index')
            ->with('success', 'Product updated successfully with variants.');
    }

    /**
     * Remove the specified product from storage.
     */
    public function destroy(Product $product)
    {
        // Ensure the vendor can only delete their own products
        if ($product->vendor_id !== auth()->user()->vendor->id) {
            abort(403, 'Unauthorized action.');
        }

        // Check if the product is in any orders
        if ($product->orderItems()->count() > 0) {
            return redirect()->route('vendor.products.index')
                ->with('error', 'Cannot delete product because it is associated with orders.');
        }

        // Remove product image
        if ($product->image_url && Storage::exists(str_replace('/storage', 'public', $product->image_url))) {
            Storage::delete(str_replace('/storage', 'public', $product->image_url));
        }

        $product->delete();

        return redirect()->route('vendor.products.index')
            ->with('success', 'Product deleted successfully.');
    }
}
