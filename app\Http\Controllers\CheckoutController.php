<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Services\PaystackService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth; // Added Auth facade
use Illuminate\Support\Str;

class CheckoutController extends Controller
{
    protected $paystackService;

    public function __construct(PaystackService $paystackService)
    {
        $this->paystackService = $paystackService;
    }

    /**
     * Display the checkout page
     */
    public function index()
    {
        $cart = session('cart', []);
        
        if (empty($cart)) {
            return redirect()->route('cart.index')
                ->with('error', 'Your cart is empty.');
        }

        $cartItems = collect($cart);
        $subtotal = $cartItems->sum(function($item) { 
            return $item['price'] * $item['quantity']; 
        });
        
        // Calculate tax
        $taxRate = config('brandify.tax_rate', 0.075); // Default to 0.075 if not set
        $tax = $subtotal * $taxRate;
        $total = $subtotal + $tax;
        
        // Get Nigerian states for dropdown
        $states = $this->getNigerianStates();

        // Determine delivery policy information
        $deliveryPolicy = null;
        $platformDeliveryProductsExist = false;
        foreach ($cartItems as $cartItem) {
            $product = Product::find($cartItem['id']);
            if ($product && $product->requires_platform_delivery) {
                $platformDeliveryProductsExist = true;
                break;
            }
        }

        if ($platformDeliveryProductsExist) {
            $deliveryPolicy = "Some items in your cart will be delivered by the platform. Platform deliveries for eligible zones (Lagos, Abuja, Ibadan, Akure) typically occur on Mondays, Wednesdays, and Fridays. For other items or zones, the vendor's delivery policy applies.";
        } else {
            $deliveryPolicy = "All items in your cart will be delivered directly by the vendors according to their individual policies.";
        }
        
        return view('checkout.index', compact('cartItems', 'subtotal', 'tax', 'total', 'states', 'deliveryPolicy'));
    }

    /**
     * Process the checkout
     */
    public function process(Request $request)
    {
        // Validate the request
        $validated = $request->validate([
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'customer_phone' => 'required|string|regex:/^0[789][01]\d{8}$/',
            'shipping_address' => 'required|string|max:500',
            'shipping_city' => 'required|string|max:100',
            'shipping_state' => 'required|string|max:100',
            'shipping_postal_code' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:500',
        ]);

        $cart = session('cart', []);
        
        if (empty($cart)) {
            return redirect()->route('cart.index')
                ->with('error', 'Your cart is empty.');
        }

        // Calculate totals
        $cartItems = collect($cart);
        $subtotal = $cartItems->sum(function($item) {
            return $item['price'] * $item['quantity'];
        });
        $taxRate = config('brandify.tax_rate', 0.075);
        $tax = $subtotal * $taxRate;
        $total = $subtotal + $tax;

        // Create the order
        $order = Order::create([
            'user_id' => Auth::id(),
            'order_number' => 'ORD-' . strtoupper(Str::random(10)),
            'total' => $total,
            'status' => 'pending',
            'payment_status' => 'pending',
            'customer_name' => $validated['customer_name'],
            'customer_email' => $validated['customer_email'],
            'customer_phone' => $validated['customer_phone'],
            'shipping_address' => $validated['shipping_address'],
            'shipping_city' => $validated['shipping_city'],
            'shipping_state' => $validated['shipping_state'],
            'shipping_postal_code' => $validated['shipping_postal_code'],
            'notes' => $validated['notes'],
        ]);

        // Create order items
        foreach ($cart as $productId => $item) {
            $product = Product::find($productId);
            
            if (!$product) continue;
            
            // Determine vendor_id
            $vendorId = $product->vendor_id;
            
            // Create order item
            OrderItem::create([
                'order_id' => $order->id,
                'vendor_id' => $vendorId,
                'product_id' => $productId,
                'product_name' => $item['name'],
                'quantity' => $item['quantity'],
                // 'price' field is removed as it's redundant with unit_price and subtotal,
                // and not in the order_items table migration created.
                'unit_price' => $item['price'], // $item['price'] from cart is the unit price
                'subtotal' => $item['price'] * $item['quantity'],
            ]);
        }

        // Clear the cart
        session()->forget('cart');

        // Initialize payment with Paystack
        $paymentData = $this->paystackService->initializeOrderPayment($order);
        
        if (isset($paymentData['status']) && $paymentData['status'] && isset($paymentData['data']['authorization_url'])) {
            return redirect($paymentData['data']['authorization_url']);
        }

        // If payment initialization failed
        return redirect()->route('orders.show', $order)
            ->with('error', 'Unable to initialize payment. Please try again.');
    }

    /**
     * Handle successful checkout
     */
    public function success(Request $request)
    {
        $orderId = $request->get('order_id');
        
        if (!$orderId) {
            return redirect()->route('home');
        }

        $order = Order::find($orderId);
        
        if (!$order || $order->user_id !== Auth::id()) {
            return redirect()->route('home');
        }

        return view('checkout.success', compact('order'));
    }

    /**
     * Get Nigerian states
     */
    private function getNigerianStates()
    {
        return [
            'Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue', 
            'Borno', 'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu', 
            'FCT', 'Gombe', 'Imo', 'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi', 
            'Kogi', 'Kwara', 'Lagos', 'Nasarawa', 'Niger', 'Ogun', 'Ondo', 
            'Osun', 'Oyo', 'Plateau', 'Rivers', 'Sokoto', 'Taraba', 'Yobe', 
            'Zamfara'
        ];
    }
}