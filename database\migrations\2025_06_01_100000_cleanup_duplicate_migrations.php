<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, let's check and fix the vendors table
        if (!Schema::hasTable('vendors')) {
            // Create vendors table if it doesn't exist
            Schema::create('vendors', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('business_name');
                $table->string('slug')->unique();
                $table->text('description')->nullable();
                $table->string('logo')->nullable();
                $table->string('banner')->nullable();
                $table->string('address_line1');
                $table->string('address_line2')->nullable();
                $table->string('city');
                $table->string('state');
                $table->string('postal_code')->nullable();
                $table->string('country')->default('Nigeria');
                $table->string('contact_phone');
                $table->string('contact_email_business');
                $table->string('business_registration_number')->nullable();
                $table->string('tax_identification_number')->nullable();
                $table->json('bank_account_details')->nullable();
                $table->enum('delivery_zone_type', ['platform_delivery_zone', 'other_states_zone']);
                $table->text('delivery_policy_text')->nullable();
                $table->enum('subscription_status', ['active', 'inactive', 'pending_payment', 'expired'])->default('pending_payment');
                $table->timestamp('subscription_expires_at')->nullable();
                $table->timestamp('last_subscription_payment_at')->nullable();
                $table->boolean('is_approved')->default(false);
                $table->boolean('is_featured')->default(false);
                $table->timestamps();
            });
        } else {
            // Add missing columns to vendors table if they don't exist
            Schema::table('vendors', function (Blueprint $table) {
                if (!Schema::hasColumn('vendors', 'slug')) {
                    $table->string('slug')->unique()->after('business_name');
                }
                if (!Schema::hasColumn('vendors', 'description')) {
                    $table->text('description')->nullable()->after('slug');
                }
                if (!Schema::hasColumn('vendors', 'logo')) {
                    $table->string('logo')->nullable()->after('description');
                }
                if (!Schema::hasColumn('vendors', 'banner')) {
                    $table->string('banner')->nullable()->after('logo');
                }
                if (!Schema::hasColumn('vendors', 'address_line2')) {
                    $table->string('address_line2')->nullable()->after('address_line1');
                }
                if (!Schema::hasColumn('vendors', 'postal_code')) {
                    $table->string('postal_code')->nullable()->after('state');
                }
                if (!Schema::hasColumn('vendors', 'country')) {
                    $table->string('country')->default('Nigeria')->after('postal_code');
                }
                if (!Schema::hasColumn('vendors', 'business_registration_number')) {
                    $table->string('business_registration_number')->nullable();
                }
                if (!Schema::hasColumn('vendors', 'tax_identification_number')) {
                    $table->string('tax_identification_number')->nullable();
                }
                if (!Schema::hasColumn('vendors', 'bank_account_details')) {
                    $table->json('bank_account_details')->nullable();
                }
                if (!Schema::hasColumn('vendors', 'delivery_policy_text')) {
                    $table->text('delivery_policy_text')->nullable();
                }
                if (!Schema::hasColumn('vendors', 'subscription_expires_at')) {
                    $table->timestamp('subscription_expires_at')->nullable();
                }
                if (!Schema::hasColumn('vendors', 'last_subscription_payment_at')) {
                    $table->timestamp('last_subscription_payment_at')->nullable();
                }
                if (!Schema::hasColumn('vendors', 'is_approved') && Schema::hasColumn('vendors', 'approved')) {
                    $table->renameColumn('approved', 'is_approved');
                }
                if (!Schema::hasColumn('vendors', 'is_featured')) {
                    $table->boolean('is_featured')->default(false);
                }
            });
        }
        
        // Fix products table
        Schema::table('products', function (Blueprint $table) {
            // Check and add missing columns
            if (!Schema::hasColumn('products', 'sku')) {
                $table->string('sku')->nullable()->unique();
            }
            if (!Schema::hasColumn('products', 'tags')) {
                $table->json('tags')->nullable();
            }
            if (!Schema::hasColumn('products', 'views_count')) {
                $table->unsignedInteger('views_count')->default(0);
            }
            if (!Schema::hasColumn('products', 'sales_count')) {
                $table->unsignedInteger('sales_count')->default(0);
            }
            if (!Schema::hasColumn('products', 'is_featured')) {
                $table->boolean('is_featured')->default(false);
            }
            if (!Schema::hasColumn('products', 'discount_price')) {
                $table->decimal('discount_price', 10, 2)->nullable();
            }
            if (!Schema::hasColumn('products', 'image_url')) {
                $table->string('image_url')->nullable();
            }
        });
        
        // Fix order_items table
        Schema::table('order_items', function (Blueprint $table) {
            if (!Schema::hasColumn('order_items', 'vendor_id')) {
                $table->foreignId('vendor_id')->nullable()->after('order_id')->constrained('vendors');
            }
            if (!Schema::hasColumn('order_items', 'product_name')) {
                $table->string('product_name')->nullable();
            }
            if (!Schema::hasColumn('order_items', 'unit_price')) {
                $table->decimal('unit_price', 10, 2)->nullable();
            }
            if (!Schema::hasColumn('order_items', 'subtotal')) {
                $table->decimal('subtotal', 10, 2)->nullable();
            }
            if (!Schema::hasColumn('order_items', 'status')) {
                $table->string('status')->default('pending');
            }
        });
        
        // Fix orders table
        Schema::table('orders', function (Blueprint $table) {
            if (!Schema::hasColumn('orders', 'vendor_id')) {
                $table->foreignId('vendor_id')->nullable()->constrained('vendors');
            }
            if (!Schema::hasColumn('orders', 'customer_name')) {
                $table->string('customer_name')->nullable();
            }
            if (!Schema::hasColumn('orders', 'customer_email')) {
                $table->string('customer_email')->nullable();
            }
            if (!Schema::hasColumn('orders', 'customer_phone')) {
                $table->string('customer_phone')->nullable();
            }
            if (!Schema::hasColumn('orders', 'shipping_address')) {
                $table->text('shipping_address')->nullable();
            }
            if (!Schema::hasColumn('orders', 'shipping_city')) {
                $table->string('shipping_city')->nullable();
            }
            if (!Schema::hasColumn('orders', 'shipping_state')) {
                $table->string('shipping_state')->nullable();
            }
            if (!Schema::hasColumn('orders', 'shipping_postal_code')) {
                $table->string('shipping_postal_code')->nullable();
            }
            if (!Schema::hasColumn('orders', 'payment_method')) {
                $table->string('payment_method')->nullable();
            }
            if (!Schema::hasColumn('orders', 'payment_status')) {
                $table->string('payment_status')->default('pending');
            }
            if (!Schema::hasColumn('orders', 'payment_reference')) {
                $table->string('payment_reference')->nullable();
            }
            if (!Schema::hasColumn('orders', 'notes')) {
                $table->text('notes')->nullable();
            }
            if (!Schema::hasColumn('orders', 'shipment_id')) {
                $table->unsignedBigInteger('shipment_id')->nullable();
            }
            if (!Schema::hasColumn('orders', 'tracking_number')) {
                $table->string('tracking_number')->nullable();
            }
            if (!Schema::hasColumn('orders', 'delivered_at')) {
                $table->timestamp('delivered_at')->nullable();
            }
        });
        
        // Fix categories table
        Schema::table('categories', function (Blueprint $table) {
            if (!Schema::hasColumn('categories', 'description')) {
                $table->text('description')->nullable();
            }
            if (!Schema::hasColumn('categories', 'is_active')) {
                $table->boolean('is_active')->default(true);
            }
            if (!Schema::hasColumn('categories', 'order')) {
                $table->integer('order')->default(0);
            }
        });
        
        // Fix reviews table
        Schema::table('reviews', function (Blueprint $table) {
            if (!Schema::hasColumn('reviews', 'vendor_id')) {
                $table->foreignId('vendor_id')->nullable()->constrained('vendors');
            }
            if (!Schema::hasColumn('reviews', 'vendor_response')) {
                $table->text('vendor_response')->nullable();
            }
        });
        
        // Remove duplicate migration entries from migrations table
        $this->cleanupMigrationTable();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is for cleanup only, no rollback needed
    }
    
    /**
     * Clean up duplicate entries in migrations table
     */
    private function cleanupMigrationTable()
    {
        // Get all migrations
        $migrations = DB::table('migrations')->get();
        
        // Group by migration name pattern
        $patterns = [
            'create_vendors_table',
            'add_role_to_users_table',
            'add_missing_columns_to_products_table',
            'add_new_fields_to_vendors_table',
            'add_missing_fields_to_order_items_table',
        ];
        
        foreach ($patterns as $pattern) {
            $duplicates = $migrations->filter(function ($migration) use ($pattern) {
                return str_contains($migration->migration, $pattern);
            });
            
            if ($duplicates->count() > 1) {
                // Keep the first one, delete the rest
                $toDelete = $duplicates->skip(1)->pluck('migration');
                DB::table('migrations')->whereIn('migration', $toDelete)->delete();
            }
        }
    }
};