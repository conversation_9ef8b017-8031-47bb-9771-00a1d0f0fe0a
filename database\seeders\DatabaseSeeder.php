<?php
namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\Role;
use App\Models\User;
use App\Models\Vendor;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        // Create roles
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $vendorRole = Role::firstOrCreate(['name' => 'vendor']);
        $customerRole = Role::firstOrCreate(['name' => 'customer']);

        // Create admin user
        $admin = User::firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'Admin User',
            'password' => Hash::make('password'),
            'role' => 'admin',
        ]);

        // Create vendor user
        $vendorUser = User::firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'Vendor User',
            'password' => Hash::make('password'),
            'role' => 'vendor',
        ]);

        // Create vendor profile for vendor user
        Vendor::firstOrCreate([
            'user_id' => $vendorUser->id,
        ], [
            'business_name' => 'Demo Vendor Shop',
            'slug' => 'demo-vendor-shop',
            'address_line1' => '123 Demo Street',
            'city' => 'Lagos',
            'state' => 'Lagos',
            'contact_phone' => '+2348012345678',
            'contact_email_business' => '<EMAIL>',
            'delivery_zone_type' => 'platform_delivery_zone',
            'subscription_status' => 'active',
            'is_approved' => true,
            'is_featured' => true,
        ]);
        
        // Run the seeders
        $this->call([
            BrandSeeder::class,
            VendorSeeder::class,
            ProductSeeder::class
        ]);
    }
}
