@extends('layouts.vendor')

@section('content')
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0 text-gray-800">Manage Products</h1>
            <a href="{{ route('vendor.products.create') }}" class="btn btn-dark">
                <i class="fas fa-plus me-2"></i> Add New Product
            </a>
        </div>

        @if (session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        @if (session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        <div class="card border-0 shadow-sm">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th>Image</th>
                                <th>Name</th>
                                <th>Category</th>
                                <th>Price</th>
                                <th>Stock</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($products->where('vendor_id', auth()->user()->vendor->id) as $product)
                                <tr>
                                    <td>
                                        <img src="{{ $product->image_url ?? 'https://via.placeholder.com/50x50?text=No+Image' }}"
                                            alt="{{ $product->name }}" width="50" height="50" class="rounded">
                                    </td>
                                    <td>{{ $product->name }}</td>
                                    <td>{{ $product->category->name ?? 'N/A' }}</td>
                                    <td>
                                        @if ($product->discount_price)
                                            <span
                                                class="text-decoration-line-through text-muted me-2">${{ number_format($product->price, 2) }}</span>
                                            <span class="fw-bold">${{ number_format($product->discount_price, 2) }}</span>
                                        @else
                                            <span class="fw-bold">${{ number_format($product->price, 2) }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if ($product->stock_quantity > 0)
                                            <span class="badge bg-success">{{ $product->stock_quantity }} in stock</span>
                                        @else
                                            <span class="badge bg-danger">Out of stock</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if ($product->is_active)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-secondary">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="d-flex">
                                            <a href="{{ route('vendor.products.edit', $product->id) }}"
                                                class="btn btn-sm btn-outline-dark me-2" aria-label="Edit Product">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('vendor.products.destroy', $product->id) }}"
                                                method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-outline-danger"
                                                    aria-label="Delete Product"
                                                    onclick="return confirm('Are you sure you want to delete this product?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                            <h5 class="fw-bold mb-2">No Products Yet</h5>
                                            <p class="text-muted mb-3">You haven't added any products to your store.</p>
                                            <a href="{{ route('vendor.products.create') }}" class="btn btn-dark">
                                                <i class="fas fa-plus me-2"></i> Add Your First Product
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="d-flex justify-content-center mt-4">
            {{ $products->links() }}
        </div>
    </div>
@endsection
