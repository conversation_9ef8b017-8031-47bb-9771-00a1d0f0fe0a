<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->string('shipbubble_shipment_id')->unique();
            $table->string('tracking_number')->unique();
            $table->enum('status', [
                'created', 
                'picked_up', 
                'in_transit', 
                'out_for_delivery', 
                'delivered', 
                'failed', 
                'returned'
            ])->default('created');
            $table->json('pickup_address');
            $table->json('delivery_address');
            $table->json('items');
            $table->json('shipbubble_response')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('last_updated_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipments');
    }
};
