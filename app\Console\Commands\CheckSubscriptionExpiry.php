<?php

namespace App\Console\Commands;

use App\Models\Vendor;
use App\Models\Subscription;
use Illuminate\Console\Command;
use Carbon\Carbon;

class CheckSubscriptionExpiry extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscriptions:check-expiry';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for expired vendor subscriptions and update their status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking for expired subscriptions...');
        
        // Find vendors with expired subscriptions
        $expiredVendors = Vendor::where('subscription_status', 'active')
            ->where('subscription_expires_at', '<=', Carbon::now())
            ->get();
        
        $count = 0;
        
        foreach ($expiredVendors as $vendor) {
            // Update vendor subscription status
            $vendor->update([
                'subscription_status' => 'expired'
            ]);
            
            // Update subscription record if exists
            $subscription = $vendor->subscription;
            if ($subscription) {
                $subscription->update([
                    'status' => 'expired'
                ]);
            }
            
            $count++;
            $this->info("Expired subscription for vendor: {$vendor->business_name}");
            
            // TODO: Send notification email to vendor about expired subscription
        }
        
        $this->info("Total expired subscriptions processed: {$count}");
        
        // Also check for subscriptions expiring soon (within 7 days)
        $expiringVendors = Vendor::where('subscription_status', 'active')
            ->whereBetween('subscription_expires_at', [Carbon::now(), Carbon::now()->addDays(7)])
            ->get();
        
        foreach ($expiringVendors as $vendor) {
            $daysRemaining = Carbon::now()->diffInDays($vendor->subscription_expires_at);
            $this->info("Vendor {$vendor->business_name} subscription expires in {$daysRemaining} days");
            
            // TODO: Send reminder email to vendor
        }
        
        return Command::SUCCESS;
    }
}