@extends('layouts.vendor')

@push('styles')
    <style>
        .dashboard-stat-card {
            border: none;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border-radius: 1rem;
        }

        .stat-icon {
            background: #000;
            color: #fff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .badge,
        .bg-success,
        .bg-danger,
        .bg-info,
        .bg-warning {
            background: #000 !important;
            color: #fff !important;
            border-radius: 0.5rem;
            font-size: 0.9rem;
            padding: 0.4em 0.8em;
        }

        .dashboard-stat-card h3,
        .dashboard-stat-card h6,
        .fw-bold,
        h1,
        h5 {
            color: #000;
        }

        @media (max-width: 767px) {
            .dashboard-stat-card {
                margin-bottom: 1rem;
            }

            .card {
                margin-bottom: 1rem;
            }
        }
    </style>
@endpush

@section('content')
    <div class="container-fluid px-0 px-md-3">
        <div class="row g-3">
            <!-- Stats: Sales, Orders, Products, Subscription -->
            <div class="col-6 col-md-3 mb-4">
                <div class="card h-100 dashboard-stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">Total Sales</h6>
                                <h3 class="fw-bold mb-0">₦{{ number_format($totalSales, 2) }}</h3>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <span class="badge">{{ $salesGrowth >= 0 ? '+' : '' }}{{ $salesGrowth }}%</span>
                            <span class="text-muted ms-2">from last month</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-6 col-md-3 mb-4">
                <div class="card h-100 dashboard-stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">Total Orders</h6>
                                <h3 class="fw-bold mb-0">{{ $totalOrders }}</h3>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <span class="badge">{{ $orderGrowth >= 0 ? '+' : '' }}{{ $orderGrowth }}%</span>
                            <span class="text-muted ms-2">from last month</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-6 col-md-3 mb-4">
                <div class="card h-100 dashboard-stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">Total Products</h6>
                                <h3 class="fw-bold mb-0">{{ $totalProducts }}</h3>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-box"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <span class="badge">+{{ $newProductsThisMonth }}</span>
                            <span class="text-muted ms-2">new this month</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-6 col-md-3 mb-4">
                <div class="card h-100 dashboard-stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">Subscription</h6>
                                <h3 class="fw-bold mb-0">{{ $subscriptionName }}</h3>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-crown"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            @if ($daysRemaining > 0)
                                <span class="badge">{{ $daysRemaining }} {{ Str::plural('day', $daysRemaining) }}</span>
                                <span class="text-muted ms-2">remaining</span>
                            @elseif($daysRemaining == 0)
                                <span class="badge">Expires today</span>
                                <a href="{{ route('vendor.subscription.index') }}" class="text-muted ms-2">Renew now</a>
                            @else
                                <span class="badge">Expired</span>
                                <a href="{{ route('vendor.subscription.index') }}" class="text-muted ms-2">Renew now</a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body d-flex flex-wrap gap-2">
                        <a href="{{ route('vendor.products.create') }}" class="btn btn-dark flex-fill"><i
                                class="fas fa-plus me-2"></i>Add Product</a>
                        <a href="{{ route('vendor.orders.index') }}" class="btn btn-outline-dark flex-fill"><i
                                class="fas fa-shopping-cart me-2"></i>View Orders</a>
                        <a href="{{ route('vendor.earnings.index') }}" class="btn btn-outline-dark flex-fill"><i
                                class="fas fa-wallet me-2"></i>View Earnings</a>
                        <a href="{{ route('vendor.settings.profile') }}" class="btn btn-outline-dark flex-fill"><i
                                class="fas fa-user-cog me-2"></i>Profile Settings</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Orders Table -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Recent Orders</h5>
                        <a href="{{ route('vendor.orders.index') }}" class="btn btn-sm btn-dark">View All</a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped mb-0">
                                <thead>
                                    <tr>
                                        <th>Order #</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Total</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($recentOrders as $order)
                                        <tr>
                                            <td>{{ $order->order_number ?? 'ORD-' . $order->id }}</td>
                                            <td>{{ $order->created_at->format('M d, Y') }}</td>
                                            <td><span class="badge">{{ ucfirst($order->status) }}</span></td>
                                            <td>₦{{ number_format($order->total, 2) }}</td>
                                            <td><a href="{{ route('vendor.orders.show', $order->id) }}"
                                                    class="btn btn-sm btn-dark">View</a></td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="5" class="text-center">No recent orders found</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Commission/Earnings Table -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Recent Earnings</h5>
                        <a href="{{ route('vendor.earnings.index') }}" class="btn btn-sm btn-dark">View All</a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped mb-0">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Order #</th>
                                        <th>Commission</th>
                                        <th>Net Earnings</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($recentEarnings as $earning)
                                        <tr>
                                            <td>{{ $earning->created_at->format('M d, Y') }}</td>
                                            <td>{{ $earning->order->order_number ?? 'ORD-' . $earning->order_id }}</td>
                                            <td>₦{{ number_format($earning->commission_amount, 2) }}</td>
                                            <td>₦{{ number_format($earning->net_earnings, 2) }}</td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="4" class="text-center">No recent earnings found</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
