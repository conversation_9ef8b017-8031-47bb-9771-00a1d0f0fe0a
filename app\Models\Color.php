<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\ProductVariant;

/**
 * App\Models\Color
 *
 * @property int $id
 * @property string $name
 * @property string $hex_code
 *
 * @method \Illuminate\Database\Eloquent\Relations\HasMany productVariants()
 */
class Color extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'hex_code'];

    /**
     * Get the product variants associated with the color.
     */
    public function productVariants()
    {
        return $this->hasMany(ProductVariant::class);
    }

    /**
     * Get a Bootstrap badge for this color (for mobile-friendly UI).
     */
    public function badgeHtml($size = 20)
    {
        $color = htmlspecialchars($this->hex_code);
        $name = htmlspecialchars($this->name);
        return "<span title='{$name}' style='display:inline-block;width:{$size}px;height:{$size}px;border-radius:50%;background:{$color};border:1px solid #ccc;vertical-align:middle;'></span> <span class='d-none d-md-inline'>{$name}</span>";
    }
}
