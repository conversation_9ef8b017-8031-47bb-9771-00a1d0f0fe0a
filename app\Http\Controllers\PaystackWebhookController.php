<?php

namespace App\Http\Controllers;

use App\Services\PaystackService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaystackWebhookController extends Controller
{
    protected $paystackService;

    public function __construct(PaystackService $paystackService)
    {
        $this->paystackService = $paystackService;
    }

    /**
     * Handle Paystack webhook
     */
    public function handle(Request $request)
    {
        // Get the signature from headers
        $signature = $request->header('x-paystack-signature');
        
        if (!$signature) {
            Log::warning('Paystack webhook received without signature');
            return response()->json(['error' => 'No signature'], 400);
        }

        // Get the raw payload
        $payload = $request->getContent();
        
        // Verify the signature
        if (!$this->paystackService->verifyWebhookSignature($signature, $payload)) {
            Log::warning('Paystack webhook signature verification failed');
            return response()->json(['error' => 'Invalid signature'], 400);
        }

        // Process the webhook
        $data = json_decode($payload, true);
        
        try {
            $result = $this->paystackService->handleWebhook($data);
            
            if ($result) {
                return response()->json(['status' => 'success'], 200);
            } else {
                return response()->json(['status' => 'failed'], 400);
            }
        } catch (\Exception $e) {
            Log::error('Paystack webhook processing error: ' . $e->getMessage());
            return response()->json(['error' => 'Processing failed'], 500);
        }
    }
}