<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Vendor;
use App\Models\Product;
use App\Models\Commission;
use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AnalyticsController extends Controller
{
    public function index()
    {
        // Calculate date ranges
        $currentMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();
        $lastMonthEnd = Carbon::now()->subMonth()->endOfMonth();
        
        // Total Revenue
        $totalRevenue = Order::where('payment_status', 'paid')
            ->sum('total_amount');
            
        $lastMonthRevenue = Order::where('payment_status', 'paid')
            ->whereBetween('created_at', [$lastMonth, $lastMonthEnd])
            ->sum('total_amount');
            
        $revenueGrowth = $lastMonthRevenue > 0 
            ? round((($totalRevenue - $lastMonthRevenue) / $lastMonthRevenue) * 100, 2) 
            : 0;
        
        // Total Commission
        $totalCommission = Commission::sum('amount');
        
        // Active Vendors
        $activeVendors = Vendor::where('subscription_status', 'active')
            ->where('is_approved', true)
            ->count();
        
        // Total Orders
        $totalOrders = Order::count();
        $lastMonthOrders = Order::whereBetween('created_at', [$lastMonth, $lastMonthEnd])->count();
        $orderGrowth = $lastMonthOrders > 0 
            ? round((($totalOrders - $lastMonthOrders) / $lastMonthOrders) * 100, 2) 
            : 0;
        
        // Revenue Chart Data (Last 12 months)
        $revenueChartData = $this->getRevenueChartData();
        
        // Order Status Distribution
        $orderStatusData = $this->getOrderStatusData();
        
        // Top Vendors
        $topVendors = Vendor::select('vendors.*')
            ->selectRaw('SUM(order_items.subtotal) as total_sales')
            ->selectRaw('SUM(order_items.commission_amount_calculated) as total_commission')
            ->selectRaw('COUNT(DISTINCT orders.id) as order_count')
            ->join('products', 'vendors.id', '=', 'products.vendor_id')
            ->join('order_items', 'products.id', '=', 'order_items.product_id')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->where('orders.payment_status', 'paid')
            ->groupBy('vendors.id')
            ->orderBy('total_sales', 'desc')
            ->limit(5)
            ->get();
        
        // Top Products
        $topProducts = Product::select('products.*')
            ->selectRaw('SUM(order_items.quantity) as units_sold')
            ->selectRaw('SUM(order_items.subtotal) as revenue')
            ->join('order_items', 'products.id', '=', 'order_items.product_id')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->where('orders.payment_status', 'paid')
            ->groupBy('products.id')
            ->orderBy('units_sold', 'desc')
            ->with('vendor')
            ->limit(5)
            ->get();
        
        // Subscription Analytics
        $platformZoneVendors = Vendor::where('delivery_zone_type', 'platform_delivery_zone')
            ->where('subscription_status', 'active')
            ->count();
            
        $otherZoneVendors = Vendor::where('delivery_zone_type', 'other_states_zone')
            ->where('subscription_status', 'active')
            ->count();
            
        $monthlySubscriptionRevenue = ($platformZoneVendors * 10000) + ($otherZoneVendors * 7000);
        
        $expiringSubscriptions = Vendor::where('subscription_status', 'active')
            ->whereBetween('subscription_expires_at', [Carbon::now(), Carbon::now()->addDays(7)])
            ->count();
        
        return view('admin.analytics.index', compact(
            'totalRevenue', 'revenueGrowth', 'totalCommission', 'activeVendors',
            'totalOrders', 'orderGrowth', 'topVendors', 'topProducts',
            'platformZoneVendors', 'otherZoneVendors', 'monthlySubscriptionRevenue',
            'expiringSubscriptions', 'revenueChartData', 'orderStatusData'
        ) + $revenueChartData + $orderStatusData);
    }
    
    private function getRevenueChartData()
    {
        $data = [];
        $labels = [];
        $revenueData = [];
        $commissionData = [];
        
        for ($i = 11; $i >= 0; $i--) {
            $startDate = Carbon::now()->subMonths($i)->startOfMonth();
            $endDate = Carbon::now()->subMonths($i)->endOfMonth();
            
            $labels[] = $startDate->format('M Y');
            
            $revenue = Order::where('payment_status', 'paid')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->sum('total_amount');
            $revenueData[] = $revenue;
            
            $commission = Commission::whereBetween('created_at', [$startDate, $endDate])
                ->sum('amount');
            $commissionData[] = $commission;
        }
        
        return [
            'revenueLabels' => $labels,
            'revenueData' => $revenueData,
            'commissionData' => $commissionData,
        ];
    }
    
    private function getOrderStatusData()
    {
        $statuses = Order::select('status', DB::raw('COUNT(*) as count'))
            ->groupBy('status')
            ->get();
        
        $labels = [];
        $data = [];
        
        foreach ($statuses as $status) {
            $labels[] = ucfirst(str_replace('_', ' ', $status->status));
            $data[] = $status->count;
        }
        
        return [
            'orderStatusLabels' => $labels,
            'orderStatusData' => $data,
        ];
    }
}