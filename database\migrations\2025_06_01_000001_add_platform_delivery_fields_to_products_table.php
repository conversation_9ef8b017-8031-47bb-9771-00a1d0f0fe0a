<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->boolean('requires_platform_delivery')->default(false)->after('is_active');
            $table->decimal('weight', 8, 2)->nullable()->after('stock_quantity')->comment('Weight in kg');
            $table->json('dimensions')->nullable()->after('weight')->comment('Length, width, height in cm');
            $table->string('shipping_class')->nullable()->after('dimensions');
            $table->string('meta_title')->nullable()->after('description');
            $table->text('meta_description')->nullable()->after('meta_title');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn([
                'requires_platform_delivery',
                'weight',
                'dimensions',
                'shipping_class',
                'meta_title',
                'meta_description'
            ]);
        });
    }
};