@extends('layouts.vendor')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">Shop Settings</h2>
            
            <form method="POST" action="{{ route('vendor.settings.update') }}" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                
                <!-- Basic Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Basic Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="business_name" class="form-label">Business Name</label>
                                <input type="text" class="form-control @error('business_name') is-invalid @enderror" 
                                       id="business_name" name="business_name" 
                                       value="{{ old('business_name', $vendor->business_name) }}" required>
                                @error('business_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="slug" class="form-label">Shop URL</label>
                                <div class="input-group">
                                    <span class="input-group-text">{{ url('/vendors') }}/</span>
                                    <input type="text" class="form-control" value="{{ $vendor->slug }}" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Shop Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description', $vendor->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="logo" class="form-label">Shop Logo</label>
                                @if($vendor->logo)
                                    <div class="mb-2">
                                        <img src="{{ Storage::url($vendor->logo) }}" alt="Current Logo" style="max-height: 100px;">
                                    </div>
                                @endif
                                <input type="file" class="form-control @error('logo') is-invalid @enderror" 
                                       id="logo" name="logo" accept="image/*">
                                <small class="text-muted">Recommended: 200x200px, Max: 2MB</small>
                                @error('logo')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="banner" class="form-label">Shop Banner</label>
                                @if($vendor->banner)
                                    <div class="mb-2">
                                        <img src="{{ Storage::url($vendor->banner) }}" alt="Current Banner" style="max-height: 100px; max-width: 100%;">
                                    </div>
                                @endif
                                <input type="file" class="form-control @error('banner') is-invalid @enderror" 
                                       id="banner" name="banner" accept="image/*">
                                <small class="text-muted">Recommended: 1200x300px, Max: 4MB</small>
                                @error('banner')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Contact Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="contact_phone" class="form-label">Business Phone</label>
                                <input type="tel" class="form-control @error('contact_phone') is-invalid @enderror" 
                                       id="contact_phone" name="contact_phone" 
                                       value="{{ old('contact_phone', $vendor->contact_phone) }}" required>
                                @error('contact_phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="contact_email_business" class="form-label">Business Email</label>
                                <input type="email" class="form-control @error('contact_email_business') is-invalid @enderror" 
                                       id="contact_email_business" name="contact_email_business" 
                                       value="{{ old('contact_email_business', $vendor->contact_email_business) }}" required>
                                @error('contact_email_business')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="address_line1" class="form-label">Address Line 1</label>
                            <input type="text" class="form-control @error('address_line1') is-invalid @enderror" 
                                   id="address_line1" name="address_line1" 
                                   value="{{ old('address_line1', $vendor->address_line1) }}" required>
                            @error('address_line1')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="address_line2" class="form-label">Address Line 2 (Optional)</label>
                            <input type="text" class="form-control @error('address_line2') is-invalid @enderror" 
                                   id="address_line2" name="address_line2" 
                                   value="{{ old('address_line2', $vendor->address_line2) }}">
                            @error('address_line2')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="city" class="form-label">City</label>
                                <input type="text" class="form-control @error('city') is-invalid @enderror" 
                                       id="city" name="city" 
                                       value="{{ old('city', $vendor->city) }}" required>
                                @error('city')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="state" class="form-label">State</label>
                                <input type="text" class="form-control" 
                                       value="{{ $vendor->state }}" readonly>
                                <input type="hidden" name="state" value="{{ $vendor->state }}">
                                <small class="text-muted">Contact support to change state</small>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="postal_code" class="form-label">Postal Code</label>
                                <input type="text" class="form-control @error('postal_code') is-invalid @enderror" 
                                       id="postal_code" name="postal_code" 
                                       value="{{ old('postal_code', $vendor->postal_code) }}">
                                @error('postal_code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Banking Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Banking Information</h5>
                    </div>
                    <div class="card-body">
                        @php
                            $bankDetails = is_string($vendor->bank_account_details) 
                                ? json_decode($vendor->bank_account_details, true) 
                                : $vendor->bank_account_details;
                        @endphp
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="bank_name" class="form-label">Bank Name</label>
                                <input type="text" class="form-control @error('bank_name') is-invalid @enderror" 
                                       id="bank_name" name="bank_name" 
                                       value="{{ old('bank_name', $bankDetails['bank_name'] ?? '') }}">
                                @error('bank_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="account_name" class="form-label">Account Name</label>
                                <input type="text" class="form-control @error('account_name') is-invalid @enderror" 
                                       id="account_name" name="account_name" 
                                       value="{{ old('account_name', $bankDetails['account_name'] ?? '') }}">
                                @error('account_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="account_number" class="form-label">Account Number</label>
                                <input type="text" class="form-control @error('account_number') is-invalid @enderror" 
                                       id="account_number" name="account_number" 
                                       value="{{ old('account_number', $bankDetails['account_number'] ?? '') }}">
                                @error('account_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <small class="text-muted">Banking information is required for withdrawals</small>
                    </div>
                </div>
                
                <!-- Business Registration -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Business Registration</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="business_registration_number" class="form-label">Business Registration Number</label>
                                <input type="text" class="form-control @error('business_registration_number') is-invalid @enderror" 
                                       id="business_registration_number" name="business_registration_number" 
                                       value="{{ old('business_registration_number', $vendor->business_registration_number) }}">
                                @error('business_registration_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="tax_identification_number" class="form-label">Tax Identification Number</label>
                                <input type="text" class="form-control @error('tax_identification_number') is-invalid @enderror" 
                                       id="tax_identification_number" name="tax_identification_number" 
                                       value="{{ old('tax_identification_number', $vendor->tax_identification_number) }}">
                                @error('tax_identification_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Delivery Policy -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Delivery Policy</h5>
                    </div>
                    <div class="card-body">
                        @if($vendor->delivery_zone_type === 'platform_delivery_zone')
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Your products are delivered by the platform on Monday, Wednesday, and Friday.
                            </div>
                        @endif
                        
                        <div class="mb-3">
                            <label for="delivery_policy_text" class="form-label">Custom Delivery Policy</label>
                            <textarea class="form-control @error('delivery_policy_text') is-invalid @enderror" 
                                      id="delivery_policy_text" name="delivery_policy_text" 
                                      rows="4">{{ old('delivery_policy_text', $vendor->delivery_policy_text) }}</textarea>
                            <small class="text-muted">This will be displayed on your shop page</small>
                            @error('delivery_policy_text')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary">
                        Save Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection