<?php

namespace Tests\Feature\Middleware;

use Tests\TestCase;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;

class IsVendorMiddlewareTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test route that uses the middleware
        \Route::get('/test-vendor-route', function () {
            return response()->json(['success' => true]);
        })->middleware('is_vendor');
    }

    /** @test */
    public function it_redirects_unauthenticated_users_to_login()
    {
        $response = $this->get('/test-vendor-route');
        
        $response->assertRedirect(route('login'));
    }

    /** @test */
    public function it_redirects_non_vendor_users_to_home()
    {
        $user = User::factory()->create();
        
        $response = $this->actingAs($user)
            ->get('/test-vendor-route');
        
        $response->assertRedirect(route('home'))
            ->assertSessionHas('error', 'Access denied. Vendor account required.');
    }

    /** @test */
    public function it_redirects_vendors_with_inactive_subscription()
    {
        $user = User::factory()->create();
        $vendor = Vendor::factory()->create([
            'user_id' => $user->id,
            'subscription_status' => 'inactive',
            'is_approved' => true
        ]);
        
        $response = $this->actingAs($user)
            ->get('/test-vendor-route');
        
        $response->assertRedirect(route('vendor.subscription'))
            ->assertSessionHas('error', 'Please activate your subscription to continue.');
    }

    /** @test */
    public function it_redirects_unapproved_vendors()
    {
        $user = User::factory()->create();
        $vendor = Vendor::factory()->create([
            'user_id' => $user->id,
            'subscription_status' => 'active',
            'is_approved' => false
        ]);
        
        $response = $this->actingAs($user)
            ->get('/test-vendor-route');
        
        $response->assertRedirect(route('vendor.pending'))
            ->assertSessionHas('error', 'Your vendor account is pending approval.');
    }

    /** @test */
    public function it_allows_access_to_approved_vendors_with_active_subscription()
    {
        $user = User::factory()->create();
        $vendor = Vendor::factory()->create([
            'user_id' => $user->id,
            'subscription_status' => 'active',
            'is_approved' => true
        ]);
        
        $response = $this->actingAs($user)
            ->get('/test-vendor-route');
        
        $response->assertSuccessful()
            ->assertJson(['success' => true]);
    }
}
