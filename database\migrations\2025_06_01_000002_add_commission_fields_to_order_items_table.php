<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            if (!Schema::hasColumn('order_items', 'commission_rate_snapshot')) {
                $table->decimal('commission_rate_snapshot', 5, 2)->default(2.7)->after('subtotal');
            }
            if (!Schema::hasColumn('order_items', 'commission_amount_calculated')) {
                $table->decimal('commission_amount_calculated', 10, 2)->default(0)->after('commission_rate_snapshot');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->dropColumn(['commission_rate_snapshot', 'commission_amount_calculated']);
        });
    }
};