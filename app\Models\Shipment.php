<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Shipment extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'shipbubble_shipment_id',
        'tracking_number',
        'status',
        'pickup_address',
        'delivery_address',
        'items',
        'shipbubble_response',
        'delivered_at',
        'last_updated_at',
    ];

    protected $casts = [
        'pickup_address' => 'array',
        'delivery_address' => 'array',
        'items' => 'array',
        'shipbubble_response' => 'array',
        'delivered_at' => 'datetime',
        'last_updated_at' => 'datetime',
    ];

    /**
     * Get the order that owns the shipment.
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the tracking URL for this shipment
     */
    public function getTrackingUrlAttribute()
    {
        return route('shipments.track', $this->tracking_number);
    }

    /**
     * Check if the shipment is delivered
     */
    public function isDelivered()
    {
        return $this->status === 'delivered';
    }

    /**
     * Get the estimated delivery date
     */
    public function getEstimatedDeliveryDateAttribute()
    {
        // If delivered, return actual delivery date
        if ($this->delivered_at) {
            return $this->delivered_at;
        }

        // Otherwise estimate based on created date (3 days for platform zones)
        return $this->created_at->addDays(3);
    }

    /**
     * Get the delivery timeline steps
     */
    public function getTimelineAttribute()
    {
        $timeline = [
            [
                'status' => 'created',
                'title' => 'Order Shipped',
                'description' => 'Your order has been shipped',
                'date' => $this->created_at,
                'completed' => true,
            ],
            [
                'status' => 'picked_up',
                'title' => 'Picked Up',
                'description' => 'Package picked up by delivery partner',
                'date' => null,
                'completed' => in_array($this->status, ['picked_up', 'in_transit', 'out_for_delivery', 'delivered']),
            ],
            [
                'status' => 'in_transit',
                'title' => 'In Transit',
                'description' => 'Package is on the way',
                'date' => null,
                'completed' => in_array($this->status, ['in_transit', 'out_for_delivery', 'delivered']),
            ],
            [
                'status' => 'out_for_delivery',
                'title' => 'Out for Delivery',
                'description' => 'Package is out for delivery',
                'date' => null,
                'completed' => in_array($this->status, ['out_for_delivery', 'delivered']),
            ],
            [
                'status' => 'delivered',
                'title' => 'Delivered',
                'description' => 'Package has been delivered',
                'date' => $this->delivered_at,
                'completed' => $this->status === 'delivered',
            ],
        ];

        return $timeline;
    }
}
