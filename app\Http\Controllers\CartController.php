<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;

class CartController extends Controller
{

    /**
     * Display the cart page.
     */
    public function index()
    {
        $cart = session('cart', []);
        $cartItems = collect($cart);
        $subtotal = $cartItems->sum(function($item) { return $item['price'] * $item['quantity']; });
        $taxRate = config('brandify.tax_rate', 0.075); // Default to 0.075 if not set, consistent with CheckoutController
        $tax = $subtotal * $taxRate;
        $total = $subtotal + $tax;
        
        // Get recommended products based on cart contents
        $recommendedProducts = $this->getRecommendedProducts($cartItems);
        
        return view('cart.index', compact('cartItems', 'subtotal', 'tax', 'total', 'recommendedProducts'));
    }
    
    /**
     * Get recommended products based on cart items.
     *
     * @param \Illuminate\Support\Collection $cartItems
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function getRecommendedProducts($cartItems)
    {
        if ($cartItems->isEmpty()) {
            // If cart is empty, return trending products
            return Product::where('is_active', true)
                ->orderBy('created_at', 'desc')
                ->take(4)
                ->get();
        }
        
        // Get category IDs from cart items
        $productIds = $cartItems->pluck('id')->toArray();
        $relatedProducts = Product::whereIn('id', $productIds)->get();
        $categoryIds = $relatedProducts->pluck('category_id')->filter()->unique()->toArray();
        
        // Get vendor IDs from cart items
        $vendorIds = $relatedProducts->pluck('vendor_id')->filter()->unique()->toArray();
        
        // Get products from same categories or vendors but exclude products already in cart
        $recommendations = Product::where('is_active', true)
            ->where(function($query) use ($categoryIds, $vendorIds) {
                $query->whereIn('category_id', $categoryIds)
                    ->orWhereIn('vendor_id', $vendorIds);
            })
            ->whereNotIn('id', $productIds)
            ->inRandomOrder()
            ->take(8)
            ->get();
        
        // If we don't have enough recommendations, add some trending products
        if ($recommendations->count() < 4) {
            $additionalProducts = Product::where('is_active', true)
                ->whereNotIn('id', $productIds)
                ->whereNotIn('id', $recommendations->pluck('id')->toArray())
                ->orderBy('created_at', 'desc')
                ->take(4 - $recommendations->count())
                ->get();
                
            $recommendations = $recommendations->merge($additionalProducts);
        }
        
        // Limit to 4 products for display
        return $recommendations->take(4);
    }

    /**
     * Add a product to the cart.
     */
    public function add(Request $request, Product $product)
    {
        $quantity = (int)$request->input('quantity', 1);
        if ($quantity <= 0) $quantity = 1;
        
        $isAjax = $request->ajax() || $request->wantsJson() || $request->header('X-Requested-With') === 'XMLHttpRequest';
        
        // Check if product is active
        if (!$product->is_active) {
            if ($isAjax) {
                return response()->json([
                    'success' => false,
                    'message' => 'This product is not available.'
                ], 400);
            }
            return redirect()->back()->with('error', 'This product is not available.');
        }
        
        // Check stock availability
        if (isset($product->stock_quantity) && $product->stock_quantity < $quantity) {
            if ($isAjax) {
                return response()->json([
                    'success' => false,
                    'message' => 'Not enough stock available. Only ' . $product->stock_quantity . ' items left.'
                ], 400);
            }
            return redirect()->back()->with('error', 'Not enough stock available. Only ' . $product->stock_quantity . ' items left.');
        }
        
        // Get current cart
        $cart = session('cart', []);
        
        // Check if the product is already in the cart
        if (isset($cart[$product->id])) {
            // Update quantity
            $cart[$product->id]['quantity'] += $quantity;
            
            // Check again for stock availability after adding to existing quantity
            if (isset($product->stock_quantity) && $cart[$product->id]['quantity'] > $product->stock_quantity) {
                $cart[$product->id]['quantity'] = $product->stock_quantity;
                
                session(['cart' => $cart]);
                
                if ($isAjax) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Cart updated with maximum available stock.',
                        'cart_count' => collect($cart)->sum('quantity')
                    ]);
                }
                
                return redirect()->back()->with('info', 'We only have ' . $product->stock_quantity . ' items in stock. Your cart has been updated.');
            }
        } else {
            // Add new product to cart
            $cart[$product->id] = [
                'id' => $product->id,
                'name' => $product->name,
                'quantity' => $quantity,
                'price' => $product->sale_price ?? $product->price,
                'image' => $product->image_url ?? $product->image,
                'vendor_id' => $product->vendor_id,
                'vendor_name' => $product->vendor ? $product->vendor->business_name : 'Unknown Vendor'
            ];
        }
        
        // Store updated cart in session
        session(['cart' => $cart]);
        
        if ($isAjax) {
            return response()->json([
                'success' => true,
                'message' => 'Product added to cart successfully!',
                'cart_count' => collect($cart)->sum('quantity'),
                'cart_total' => collect($cart)->sum(function($item) {
                    return $item['price'] * $item['quantity'];
                })
            ]);
        }
        
        return redirect()->back()->with('success', 'Product added to cart successfully!');
    }

    /**
     * Update the quantity of a product in the cart.
     */
    public function update(Request $request, $id)
    {
        $cart = session()->get('cart', []);
        $quantity = $request->input('quantity');

        if (isset($cart[$id]) && $quantity > 0) {
            $cart[$id]['quantity'] = $quantity;
            session()->put('cart', $cart);

            // Recalculate totals
            $subtotal = 0;
            foreach ($cart as $item) {
                $subtotal += $item['price'] * $item['quantity'];
            }
            $taxRate = config('brandify.tax_rate', 0.075);
            $tax = $subtotal * $taxRate;
            $total = $subtotal + $tax;
            $item_subtotal = $cart[$id]['price'] * $cart[$id]['quantity'];

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Cart updated successfully.',
                    'cart' => [
                        'subtotal' => $subtotal,
                        'tax' => $tax,
                        'total' => $total,
                        'item_subtotal' => $item_subtotal,
                        'count' => count($cart)
                    ]
                ]);
            }

            return redirect()->route('cart.index')->with('success', 'Cart updated successfully.');
        } elseif (isset($cart[$id]) && $quantity <= 0) {
            // If quantity is 0 or less, treat as remove
            unset($cart[$id]);
            session()->put('cart', $cart);
            // Recalculate totals
            $subtotal = 0;
            foreach ($cart as $item) {
                $subtotal += $item['price'] * $item['quantity'];
            }
            $taxRate = config('brandify.tax_rate', 0.075);
            $tax = $subtotal * $taxRate;
            $total = $subtotal + $tax;

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Item removed due to zero quantity.', // Or 'Cart updated successfully.'
                    'cart' => [
                        'subtotal' => $subtotal,
                        'tax' => $tax,
                        'total' => $total,
                        'count' => count($cart)
                    ]
                ]);
            }
            return redirect()->route('cart.index')->with('success', 'Item removed from cart.');
        }

        if ($request->ajax() || $request->wantsJson()) {
            return response()->json(['success' => false, 'message' => 'Item not found in cart or invalid quantity.'], 404);
        }

        return redirect()->route('cart.index')->with('error', 'Item not found in cart or invalid quantity.');
    }

    /**
     * Remove a product from the cart.
     */
    public function remove(Request $request, $id)
    {
        $cart = session()->get('cart', []);

        if (isset($cart[$id])) {
            unset($cart[$id]);
            session()->put('cart', $cart);

            // Recalculate totals
            $subtotal = 0;
            foreach ($cart as $item) {
                $subtotal += $item['price'] * $item['quantity'];
            }
            $taxRate = config('brandify.tax_rate', 0.075);
            $tax = $subtotal * $taxRate;
            $total = $subtotal + $tax;

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Item removed from cart successfully.',
                    'cart' => [
                        'subtotal' => $subtotal,
                        'tax' => $tax,
                        'total' => $total,
                        'count' => count($cart)
                    ]
                ]);
            }

            return redirect()->route('cart.index')->with('success', 'Item removed from cart successfully.');
        }

        if ($request->ajax() || $request->wantsJson()) {
            return response()->json(['success' => false, 'message' => 'Item not found in cart.'], 404);
        }

        return redirect()->route('cart.index')->with('error', 'Item not found in cart.');
    }

    /**
     * Clear the entire cart.
     */
    public function clear(Request $request)
    {
        session()->forget('cart');

        if ($request->ajax() || $request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Cart cleared successfully.',
                'cart' => [
                    'subtotal' => 0,
                    'tax' => 0,
                    'total' => 0,
                    'count' => 0
                ]
            ]);
        }

        return redirect()->route('cart.index')->with('success', 'Cart cleared successfully.');
    }
}
