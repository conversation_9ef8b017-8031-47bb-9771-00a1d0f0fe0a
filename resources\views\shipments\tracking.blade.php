@extends('layouts.app')

@section('title', 'Track Your Order')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Track Your Order</h1>
                    <p class="text-gray-600 mt-1">Order #{{ $order->order_number }}</p>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-500">Tracking Number</p>
                    <p class="text-lg font-semibold text-blue-600">{{ $shipment->tracking_number }}</p>
                </div>
            </div>
        </div>

        <!-- Current Status -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    @if($shipment->status === 'delivered')
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    @elseif(in_array($shipment->status, ['in_transit', 'out_for_delivery']))
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                    @else
                        <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    @endif
                </div>
                <div class="ml-4">
                    <h2 class="text-xl font-semibold text-gray-900">
                        {{ ucfirst(str_replace('_', ' ', $shipment->status)) }}
                    </h2>
                    <p class="text-gray-600">
                        @if($shipment->status === 'delivered')
                            Your package was delivered on {{ $shipment->delivered_at->format('M d, Y \a\t g:i A') }}
                        @elseif($shipment->status === 'out_for_delivery')
                            Your package is out for delivery today
                        @elseif($shipment->status === 'in_transit')
                            Your package is on the way
                        @else
                            Estimated delivery: {{ $shipment->estimated_delivery_date->format('M d, Y') }}
                        @endif
                    </p>
                </div>
            </div>
        </div>

        <!-- Delivery Timeline -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Delivery Progress</h3>
            @include('shipments._timeline', ['timeline' => $timeline])
        </div>

        <!-- Order Details -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Details</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Delivery Address -->
                <div>
                    <h4 class="font-medium text-gray-900 mb-2">Delivery Address</h4>
                    <div class="text-gray-600">
                        <p>{{ $shipment->delivery_address['name'] }}</p>
                        <p>{{ $shipment->delivery_address['address'] }}</p>
                        <p>{{ $shipment->delivery_address['city'] }}, {{ $shipment->delivery_address['state'] }}</p>
                        <p>{{ $shipment->delivery_address['phone'] }}</p>
                    </div>
                </div>

                <!-- Order Items -->
                <div>
                    <h4 class="font-medium text-gray-900 mb-2">Items ({{ $order->orderItems->count() }})</h4>
                    <div class="space-y-2">
                        @foreach($order->orderItems as $item)
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">{{ $item->product->name }} × {{ $item->quantity }}</span>
                                <span class="text-gray-900">₦{{ number_format($item->price * $item->quantity) }}</span>
                            </div>
                        @endforeach
                        <div class="border-t pt-2 mt-2">
                            <div class="flex justify-between font-medium">
                                <span>Total</span>
                                <span>₦{{ number_format($order->total) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="bg-gray-50 rounded-lg p-6">
            <div class="text-center">
                <h3 class="text-lg font-medium text-gray-900 mb-2">Need Help?</h3>
                <p class="text-gray-600 mb-4">If you have any questions about your delivery, we're here to help.</p>
                <a href="mailto:<EMAIL>" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    Contact Support
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
