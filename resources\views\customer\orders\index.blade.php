@extends('layouts.app')

@section('content')
    <div class="container py-4">
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('home') }}"
                                class="text-decoration-none text-dark">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}"
                                class="text-decoration-none text-dark">My Account</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Orders</li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <h1 class="fw-bold mb-4">My Orders</h1>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-3 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="rounded-circle bg-dark text-white d-flex align-items-center justify-content-center me-3"
                                style="width: 48px; height: 48px;">
                                {{ auth()->user()->initials() }}
                            </div>
                            <div>
                                <h5 class="fw-bold mb-0">{{ auth()->user()->name }}</h5>
                                <p class="text-muted mb-0">{{ auth()->user()->email }}</p>
                            </div>
                        </div>

                        <div class="list-group list-group-flush">
                            <a href="{{ route('dashboard') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                            </a>
                            <a href="{{ route('orders.index') }}" class="list-group-item list-group-item-action active">
                                <i class="fas fa-shopping-bag me-2"></i> My Orders
                            </a>
                            <a href="{{ route('wishlist.index') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-heart me-2"></i> My Wishlist
                            </a>
                            <a href="{{ route('settings.profile') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-user-cog me-2"></i> Account Settings
                            </a>
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="list-group-item list-group-item-action text-danger">
                                    <i class="fas fa-sign-out-alt me-2"></i> Logout
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-9">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-0">
                        @if (auth()->user()->orders()->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th scope="col" class="border-0">Order</th>
                                            <th scope="col" class="border-0">Date</th>
                                            <th scope="col" class="border-0">Status</th>
                                            <th scope="col" class="border-0">Total</th>
                                            <th scope="col" class="border-0">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($orders as $order)
                                            {{-- Use paginated $orders from controller --}}
                                            <tr>
                                                <td>
                                                    <span class="fw-bold">#{{ $order->order_number }}</span>
                                                </td>
                                                <td>{{ $order->created_at->format('M d, Y') }}</td>
                                                <td>
                                                    @if ($order->status === 'completed')
                                                        <span class="badge bg-success">Completed</span>
                                                    @elseif($order->status === 'processing')
                                                        <span class="badge bg-warning text-dark">Processing</span>
                                                    @elseif($order->status === 'shipped')
                                                        <span class="badge bg-info">Shipped</span>
                                                    @elseif($order->status === 'cancelled')
                                                        <span class="badge bg-danger">Cancelled</span>
                                                    @else
                                                        <span
                                                            class="badge bg-secondary">{{ ucfirst($order->status) }}</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <span
                                                        class="fw-bold">{{ config('brandify.currency.symbol', '₦') }}{{ number_format($order->total, 2) }}</span>
                                                </td>
                                                <td>
                                                    <a href="{{ route('orders.show', $order) }}"
                                                        class="btn btn-sm btn-dark">
                                                        <i class="fas fa-eye me-1"></i> View
                                                    </a>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="5" class="text-center py-4">
                                                    <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                                                    <p class="mb-0">You haven't placed any orders yet.</p>
                                                </td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                            @if ($orders->hasPages())
                                <div class="card-footer bg-white border-top-0 pt-0">
                                    {{ $orders->links() }}
                                </div>
                            @endif
                        @else
                            <div class="text-center py-5">
                                <i class="fas fa-shopping-bag fa-4x text-muted mb-3"></i>
                                <h4 class="fw-bold mb-2">No orders found</h4>
                                <p class="text-muted mb-4">You haven't placed any orders yet.</p>
                                <a href="{{ route('products.index') }}" class="btn btn-dark">
                                    <i class="fas fa-shopping-bag me-2"></i> Start Shopping
                                </a>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Order Summary -->
                @if ($orders->total() > 0)
                    {{-- Use $orders->total() for paginated count --}}
                    <div class="row mt-4">
                        <div class="col-md-4 mb-4">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="fw-bold mb-0">Total Orders</h5>
                                        <div class="rounded-circle bg-light d-flex align-items-center justify-content-center"
                                            style="width: 48px; height: 48px;">
                                            <i class="fas fa-shopping-bag text-dark"></i>
                                        </div>
                                    </div>
                                    <h3 class="fw-bold">{{ $orders->total() }}</h3>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="fw-bold mb-0">Total Spent</h5>
                                        <div class="rounded-circle bg-light d-flex align-items-center justify-content-center"
                                            style="width: 48px; height: 48px;">
                                            <i class="fas fa-dollar-sign text-success"></i>
                                        </div>
                                    </div>
                                    {{-- For total spent, summing across all orders (not just paginated) might require a separate query in controller or a user attribute --}}
                                    <h3 class="fw-bold">
                                        {{ config('brandify.currency.symbol', '₦') }}{{ number_format(auth()->user()->orders()->sum('total'), 2) }}
                                    </h3>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="fw-bold mb-0">Completed Orders</h5>
                                        <div class="rounded-circle bg-light d-flex align-items-center justify-content-center"
                                            style="width: 48px; height: 48px;">
                                            <i class="fas fa-check-circle text-success"></i>
                                        </div>
                                    </div>
                                    <h3 class="fw-bold">
                                        {{ auth()->user()->orders()->where('status', 'completed')->count() }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Recently Viewed Products -->
                <div class="mt-5">
                    <h3 class="fw-bold mb-4">Recently Viewed Products</h3>

                    <div class="row">
                        @forelse($recentlyViewedProducts as $product)
                            <div class="col-md-4 mb-4">
                                <div class="card h-100 border-2 hover-border-dark">
                                    <div class="position-relative">
                                        <img src="{{ $product->image_url ?? 'https://via.placeholder.com/300x300?text=' . $product->name }}"
                                            alt="{{ $product->name }}" class="card-img-top">
                                        @if ($product->discount_price && $product->discount_price < $product->price)
                                            <div
                                                class="position-absolute top-2 start-2 bg-danger text-white text-xs fw-bold px-2 py-1 rounded">
                                                SALE</div>
                                        @endif
                                    </div>
                                    <div class="card-body p-4">
                                        <p class="text-muted small mb-1">{{ $product->category->name ?? 'Uncategorized' }}
                                        </p>
                                        <h5 class="fw-semibold mb-1">{{ $product->name }}</h5>
                                        @if ($product->discount_price && $product->discount_price < $product->price)
                                            <div class="d-flex align-items-center mb-3">
                                                <p class="fw-bold me-2 mb-0">
                                                    {{ config('brandify.currency.symbol', '₦') }}{{ number_format($product->discount_price, 2) }}
                                                </p>
                                                <p class="text-muted text-decoration-line-through mb-0">
                                                    {{ config('brandify.currency.symbol', '₦') }}{{ number_format($product->price, 2) }}
                                                </p>
                                            </div>
                                        @else
                                            <p class="fw-bold mb-3">
                                                {{ config('brandify.currency.symbol', '₦') }}{{ number_format($product->price, 2) }}
                                            </p>
                                        @endif
                                        <div class="d-flex justify-content-between align-items-center">
                                            <form action="{{ route('cart.add', $product) }}" method="POST">
                                                @csrf
                                                <input type="hidden" name="quantity" value="1">
                                                <button type="submit"
                                                    class="btn btn-dark d-flex align-items-center justify-content-center">
                                                    <i class="fa-solid fa-cart-plus me-2"></i> Add to Cart
                                                </button>
                                            </form>
                                            @auth
                                                <form action="{{ route('wishlist.add', $product) }}" method="POST">
                                                    @csrf
                                                    <button type="submit" class="btn btn-link text-dark p-0">
                                                        <i class="far fa-heart fs-5"></i>
                                                    </button>
                                                </form>
                                            @endauth
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <p class="mb-0">You haven't viewed any products yet. <a
                                            href="{{ route('products.index') }}" class="alert-link">Start shopping</a> to
                                        see your recently viewed products here.</p>
                                </div>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
