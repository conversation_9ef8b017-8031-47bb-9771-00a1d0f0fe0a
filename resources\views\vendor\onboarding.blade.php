@extends('layouts.app')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-warning">
                    <h4 class="mb-0">Vendor Account Pending Approval</h4>
                </div>
                <div class="card-body">
                    <div class="text-center py-4">
                        <i class="fas fa-clock fa-4x text-warning mb-3"></i>
                        <h5>Your vendor application is under review</h5>
                        <p class="text-muted">
                            Thank you for registering as a vendor on {{ config('app.name') }}. 
                            Our team is currently reviewing your application.
                        </p>
                        <p class="text-muted">
                            This process typically takes 1-2 business days. You will receive an email 
                            notification once your account has been approved.
                        </p>
                    </div>
                    
                    <hr>
                    
                    <h5 class="mb-3">Application Details</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Business Name:</strong> {{ $vendor->business_name }}</p>
                            <p><strong>Contact Email:</strong> {{ $vendor->contact_email_business }}</p>
                            <p><strong>Phone:</strong> {{ $vendor->contact_phone }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Location:</strong> {{ $vendor->city }}, {{ $vendor->state }}</p>
                            <p><strong>Delivery Zone:</strong> 
                                @if($vendor->delivery_zone_type === 'platform_delivery_zone')
                                    <span class="badge bg-primary">Platform Delivery</span>
                                @else
                                    <span class="badge bg-secondary">Self Delivery</span>
                                @endif
                            </p>
                            <p><strong>Applied On:</strong> {{ $vendor->created_at->format('M d, Y') }}</p>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h5 class="mb-3">Next Steps</h5>
                    <div class="alert alert-info">
                        <h6 class="alert-heading">After Approval:</h6>
                        <ol class="mb-0">
                            <li>You'll receive an approval email with login instructions</li>
                            <li>Complete your vendor profile with additional details</li>
                            <li>Pay your subscription fee to activate your account</li>
                            <li>Start listing your products and receiving orders!</li>
                        </ol>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">Subscription Information:</h6>
                        <p class="mb-0">
                            @if($vendor->delivery_zone_type === 'platform_delivery_zone')
                                Your subscription fee will be <strong>₦10,000/month</strong> (Platform Delivery Zone)
                            @else
                                Your subscription fee will be <strong>₦7,000/month</strong> (Other States Zone)
                            @endif
                        </p>
                    </div>
                    
                    <div class="text-center">
                        <a href="{{ route('home') }}" class="btn btn-outline-dark">
                            Return to Homepage
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection