<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Vendor;
use App\Models\Category;
use Illuminate\Support\Str;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all vendors
        $vendors = Vendor::all();
        
        // Create a default category if none exists
        $category = Category::firstOrCreate(
            ['slug' => 'clothing'],
            [
                'name' => 'Clothing',
                'description' => 'Clothing and apparel',
                'is_active' => true,
                'order' => 1
            ]
        );
        
        // Create products for each vendor
        foreach ($vendors as $vendor) {
            // Create 10 products for each vendor
            for ($i = 1; $i <= 10; $i++) {
                $name = $vendor->business_name . ' Product ' . $i;
                $isFeatured = $i <= 3; // First 3 products are featured
                
                Product::updateOrCreate(
                    ['slug' => Str::slug($name)],
                    [
                        'name' => $name,
                        'description' => 'This is a sample product from ' . $vendor->business_name,
                        'price' => rand(1000, 50000) / 100, // Random price between $10 and $500
                        'sale_price' => rand(0, 1) ? rand(500, 40000) / 100 : null, // 50% chance of sale price
                        'stock' => rand(0, 100),
                        'sku' => strtoupper(Str::random(8)),
                        'is_active' => true,
                        'is_featured' => $isFeatured,
                        'vendor_id' => $vendor->id,
                        'category_id' => $category->id,
                        'created_at' => now()->subDays(rand(1, 30)) // Random creation date in the last 30 days
                    ]
                );
            }
        }
    }
}
