<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Services\ShipBubbleService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OrderController extends Controller
{
    protected $shipBubbleService;

    public function __construct(ShipBubbleService $shipBubbleService)
    {
        $this->shipBubbleService = $shipBubbleService;
    }

    /**
     * Display a listing of the orders.
     */
    public function index(Request $request)
    {
        $query = Order::query()
            ->where('vendor_id', Auth::user()->vendor->id)
            ->with(['orderItems.product', 'shipment'])
            ->latest();

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                    ->orWhere('customer_name', 'like', "%{$search}%")
                    ->orWhere('customer_email', 'like', "%{$search}%");
            });
        }

        $orders = $query->paginate(20);

        return view('vendors.orders.index', compact('orders'));
    }

    /**
     * Display the specified order.
     */
    public function show(Order $order)
    {
        // Ensure vendor can only view their own orders
        if ($order->vendor_id !== Auth::user()->vendor->id) {
            abort(403);
        }

        $order->load(['orderItems.product', 'shipment']);

        return view('vendors.orders.show', compact('order'));
    }

    /**
     * Create a shipment for the order.
     */
    public function createShipment(Order $order)
    {
        // Ensure vendor can only create shipments for their own orders
        if ($order->vendor_id !== Auth::user()->vendor->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized action.'
            ], 403);
        }

        // Check if order is eligible for shipping
        if (!$order->isEligibleForShipping()) {
            return response()->json([
                'success' => false,
                'message' => 'Order is not eligible for shipping.'
            ], 400);
        }

        try {
            // Create shipment using ShipBubble service
            $shipment = $this->shipBubbleService->createShipment($order);

            if ($shipment) {
                return response()->json([
                    'success' => true,
                    'message' => 'Shipment created successfully.',
                    'tracking_number' => $shipment->tracking_number
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to create shipment.'
            ], 500);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error creating shipment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update order status.
     */
    public function updateStatus(Request $request, Order $order)
    {
        // Ensure vendor can only update their own orders
        if ($order->vendor_id !== Auth::user()->vendor->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized action.'
            ], 403);
        }

        $request->validate([
            'status' => 'required|in:processing,shipped,delivered,cancelled'
        ]);

        try {
            $order->update(['status' => $request->status]);

            return response()->json([
                'success' => true,
                'message' => 'Order status updated successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating order status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get order statistics for vendor dashboard
     */
    public function getStats()
    {
        $vendorId = Auth::user()->vendor->id;

        $stats = [
            'total_orders' => Order::where('vendor_id', $vendorId)->count(),
            'pending_orders' => Order::where('vendor_id', $vendorId)
                ->whereIn('status', ['pending', 'processing'])
                ->count(),
            'shipped_orders' => Order::where('vendor_id', $vendorId)
                ->whereIn('status', ['shipped', 'in_transit', 'out_for_delivery'])
                ->count(),
            'delivered_orders' => Order::where('vendor_id', $vendorId)
                ->where('status', 'delivered')
                ->count(),
        ];

        return response()->json($stats);
    }
}
