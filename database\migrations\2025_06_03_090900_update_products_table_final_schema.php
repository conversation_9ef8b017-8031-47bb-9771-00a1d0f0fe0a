<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Ensure stock_quantity column exists, renaming from 'stock' if necessary
            // This check is important due to inconsistencies found.
            if (Schema::hasColumn('products', 'stock') && !Schema::hasColumn('products', 'stock_quantity')) {
                // Check the DB driver for rename syntax, this is general
                if (DB::getDriverName() === 'sqlite') {
                    // SQLite has limited ALTER TABLE capabilities, often requires complex workaround
                    // For simplicity in this context, we might assume it's not SQLite or handle it manually.
                    // Or, if this migration is run on a fresh setup, 'stock' might not exist from the old migration.
                    // A safer approach for SQLite would be to drop and recreate, but that's data-lossy.
                    // Given the context, we'll attempt a rename if not sqlite.
                } else {
                    $table->renameColumn('stock', 'stock_quantity');
                }
            } elseif (!Schema::hasColumn('products', 'stock_quantity') && !Schema::hasColumn('products', 'stock')) {
                // If neither exists, add stock_quantity (should have been in create_products_table)
                $table->integer('stock_quantity')->default(0)->after('image_url');
            }

            // Add brand_id if it doesn't exist
            if (!Schema::hasColumn('products', 'brand_id')) {
                $table->foreignId('brand_id')->nullable()->constrained()->onDelete('set null')->after('category_id');
            }

            // Add softDeletes if it doesn't exist
            if (!Schema::hasColumn('products', 'deleted_at')) {
                $table->softDeletes();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Drop columns if they were added by this migration
            // Note: Reversing rename is tricky and depends on original state.
            // For simplicity, we'll focus on what this migration *adds*.
            if (Schema::hasColumn('products', 'brand_id')) {
                // Need to drop foreign key constraint before column if it was created by this migration
                // $table->dropForeign(['brand_id']); // May need to get specific constraint name
                // $table->dropColumn('brand_id');
                // Simpler: just drop if exists, assuming it was this migration that added it.
            }
            if (Schema::hasColumn('products', 'deleted_at')) {
                $table->dropSoftDeletes();
            }

            // If we added stock_quantity because 'stock' was missing, we might drop it.
            // Or if we renamed 'stock' to 'stock_quantity', we might rename it back.
            // This part of 'down' is complex due to the corrective nature of 'up'.
            // A robust 'down' would need to know the exact state before 'up'.
            // For now, we'll keep it simple and focus on what this migration *adds*.
            // If 'stock_quantity' was 'stock' before, renaming back:
            // if (Schema::hasColumn('products', 'stock_quantity') && !Schema::hasColumn('products', 'stock')) {
            //    $table->renameColumn('stock_quantity', 'stock');
            // }
        });
    }
};