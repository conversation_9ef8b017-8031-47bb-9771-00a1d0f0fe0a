<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Shipment;
use App\Services\ShipBubbleService;
use Illuminate\Http\Request;

class TrackingController extends Controller
{
    protected $shipBubbleService;

    public function __construct(ShipBubbleService $shipBubbleService)
    {
        $this->shipBubbleService = $shipBubbleService;
    }

    /**
     * Show tracking information for a shipment
     */
    public function show($trackingNumber)
    {
        // Find the order by tracking number
        $order = Order::where('tracking_number', $trackingNumber)->first();
        
        if (!$order) {
            // Try to find by shipment tracking number
            $shipment = Shipment::where('tracking_number', $trackingNumber)->first();
            
            if (!$shipment) {
                return view('shipments.tracking', [
                    'error' => 'Tracking number not found',
                    'trackingNumber' => $trackingNumber
                ]);
            }
            
            $order = $shipment->order;
        }

        // Get real-time tracking info from ShipBubble
        $trackingInfo = null;
        if ($order->tracking_number) {
            $trackingInfo = $this->shipBubbleService->trackShipment($order->tracking_number);
        }

        return view('shipments.tracking', compact('order', 'trackingInfo', 'trackingNumber'));
    }
}