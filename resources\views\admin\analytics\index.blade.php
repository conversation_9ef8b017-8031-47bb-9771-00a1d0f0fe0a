@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <h2 class="mb-4">Analytics Dashboard</h2>
    
    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h6 class="text-muted mb-2">Total Revenue</h6>
                    <h3 class="mb-0">₦{{ number_format($totalRevenue ?? 0, 2) }}</h3>
                    <small class="text-success">+{{ $revenueGrowth ?? 0 }}% from last month</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h6 class="text-muted mb-2">Platform Commission</h6>
                    <h3 class="mb-0">₦{{ number_format($totalCommission ?? 0, 2) }}</h3>
                    <small class="text-muted">2.7% of sales</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h6 class="text-muted mb-2">Active Vendors</h6>
                    <h3 class="mb-0">{{ $activeVendors ?? 0 }}</h3>
                    <small class="text-muted">With active subscriptions</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h6 class="text-muted mb-2">Total Orders</h6>
                    <h3 class="mb-0">{{ $totalOrders ?? 0 }}</h3>
                    <small class="text-success">+{{ $orderGrowth ?? 0 }}% from last month</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Revenue Trend</h5>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart" height="100"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Order Status Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="orderStatusChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Top Vendors Table -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Top Performing Vendors</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Vendor</th>
                                    <th>Sales</th>
                                    <th>Commission</th>
                                    <th>Orders</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($topVendors ?? [] as $vendor)
                                    <tr>
                                        <td>{{ $vendor->business_name }}</td>
                                        <td>₦{{ number_format($vendor->total_sales, 2) }}</td>
                                        <td>₦{{ number_format($vendor->total_commission, 2) }}</td>
                                        <td>{{ $vendor->order_count }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">No data available</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Top Selling Products</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Vendor</th>
                                    <th>Units Sold</th>
                                    <th>Revenue</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($topProducts ?? [] as $product)
                                    <tr>
                                        <td>{{ $product->name }}</td>
                                        <td>{{ $product->vendor->business_name ?? 'N/A' }}</td>
                                        <td>{{ $product->units_sold }}</td>
                                        <td>₦{{ number_format($product->revenue, 2) }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">No data available</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Subscription Analytics -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Subscription Analytics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <h6>Platform Delivery Zone</h6>
                            <p class="h4">{{ $platformZoneVendors ?? 0 }}</p>
                            <small class="text-muted">₦10,000/month each</small>
                        </div>
                        <div class="col-md-3">
                            <h6>Other States Zone</h6>
                            <p class="h4">{{ $otherZoneVendors ?? 0 }}</p>
                            <small class="text-muted">₦7,000/month each</small>
                        </div>
                        <div class="col-md-3">
                            <h6>Monthly Subscription Revenue</h6>
                            <p class="h4">₦{{ number_format($monthlySubscriptionRevenue ?? 0, 2) }}</p>
                            <small class="text-muted">Recurring revenue</small>
                        </div>
                        <div class="col-md-3">
                            <h6>Expiring Soon</h6>
                            <p class="h4 text-warning">{{ $expiringSubscriptions ?? 0 }}</p>
                            <small class="text-muted">Within 7 days</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode($revenueLabels ?? []) !!},
        datasets: [{
            label: 'Revenue',
            data: {!! json_encode($revenueData ?? []) !!},
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }, {
            label: 'Commission',
            data: {!! json_encode($commissionData ?? []) !!},
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '₦' + value.toLocaleString();
                    }
                }
            }
        }
    }
});

// Order Status Chart
const orderStatusCtx = document.getElementById('orderStatusChart').getContext('2d');
new Chart(orderStatusCtx, {
    type: 'doughnut',
    data: {
        labels: {!! json_encode($orderStatusLabels ?? []) !!},
        datasets: [{
            data: {!! json_encode($orderStatusData ?? []) !!},
            backgroundColor: [
                'rgb(255, 205, 86)',
                'rgb(54, 162, 235)',
                'rgb(75, 192, 192)',
                'rgb(255, 99, 132)',
                'rgb(201, 203, 207)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});
</script>
@endpush
@endsection