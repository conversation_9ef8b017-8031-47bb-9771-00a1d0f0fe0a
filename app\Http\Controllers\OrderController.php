<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Product;
use App\Services\ViewedProductsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth; // Added Auth facade

class OrderController extends Controller
{
    /**
     * The viewed products service instance.
     *
     * @var \App\Services\ViewedProductsService
     */
    protected $viewedProductsService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\ViewedProductsService  $viewedProductsService
     * @return void
     */
    public function __construct(ViewedProductsService $viewedProductsService)
    {
        $this->viewedProductsService = $viewedProductsService;
    }
    
    /**
     * Display a listing of the user's orders.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        /** @var \App\Models\User|null $user */
        $user = Auth::user();
        $orders = $user ? $user->orders()->latest()->paginate(10) : collect();
        $recentlyViewedProducts = $this->viewedProductsService->getRecentlyViewedProducts(3);
        
        return view('customer.orders.index', compact('orders', 'recentlyViewedProducts'));
    }

    /**
     * Display the specified order.
     *
     * @param  \App\Models\Order  $order
     * @return \Illuminate\View\View
     */
    public function show(Order $order)
    {
        // Make sure the order belongs to the authenticated user
        if ($order->user_id !== Auth::id()) {
            return redirect()->route('orders.index')->with('error', 'You do not have access to this order.');
        }

        return view('customer.orders.show', compact('order'));
    }

    /**
     * Cancel the specified order.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Order  $order
     * @return \Illuminate\Http\RedirectResponse
     */
    public function cancel(Request $request, Order $order)
    {
        // Make sure the order belongs to the authenticated user
        if ($order->user_id !== Auth::id()) {
            return redirect()->route('orders.index')->with('error', 'You do not have access to this order.');
        }

        // Check if the order can be cancelled
        if (!$order->isPending() && !$order->isProcessing()) {
            return redirect()->route('orders.show', $order)->with('error', 'This order cannot be cancelled.');
        }

        // Update order status
        $order->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
        ]);

        return redirect()->route('orders.show', $order)->with('success', 'Order has been cancelled successfully.');
    }

    /**
     * Request return for the specified order.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Order  $order
     * @return \Illuminate\Http\RedirectResponse
     */
    public function requestReturn(Request $request, Order $order)
    {
        // Make sure the order belongs to the authenticated user
        if ($order->user_id !== Auth::id()) {
            return redirect()->route('orders.index')->with('error', 'You do not have access to this order.');
        }

        // Check if the order is completed and can be returned
        if (!$order->isCompleted()) {
            return redirect()->route('orders.show', $order)->with('error', 'Only completed orders can be returned.');
        }

        // Validate the return reason
        $request->validate([
            'return_reason' => 'required|string|max:500',
        ]);

        // Update order status
        $order->update([
            'status' => 'return_requested',
            'return_reason' => $request->return_reason,
            'return_requested_at' => now(),
        ]);

        return redirect()->route('orders.show', $order)->with('success', 'Return request submitted successfully.');
    }

    /**
     * Initiate product order payment via Paystack
     */
    public function pay(Request $request, $orderId)
    {
        $order = Order::findOrFail($orderId);
        if ($order->user_id !== Auth::id()) {
            return redirect()->route('orders.index')->with('error', 'You do not have access to this order.');
        }
        if ($order->status !== 'pending') {
            return redirect()->route('orders.show', $order)->with('error', 'Order cannot be paid.');
        }
        $paystack = new \App\Services\PaystackService();
        $user = Auth::user();
        $data = [
            'email' => $user ? $user->email : null, // Handle case where user might be null if not properly authenticated
            'amount' => $order->total * 100, // Paystack expects kobo
            'metadata' => [
                'order_id' => $order->id,
                'type' => 'order',
            ],
            'callback_url' => route('orders.paystack.callback', $order->id),
        ];
        $response = $paystack->initializePayment($data);
        if (isset($response['status']) && $response['status'] && isset($response['data']['authorization_url'])) {
            return redirect($response['data']['authorization_url']);
        }
        return back()->with('error', 'Unable to initiate payment. Please try again.');
    }

    /**
     * Handle Paystack callback for order payment
     */
    public function paystackCallback(Request $request, $orderId)
    {
        $order = Order::findOrFail($orderId);
        $reference = $request->query('reference');
        $paystack = new \App\Services\PaystackService();
        $result = $paystack->verifyPayment($reference);
        if (isset($result['status']) && $result['status'] && isset($result['data']['status']) && $result['data']['status'] === 'success') {
            $order->update(['status' => 'processing', 'payment_reference' => $reference]);
            // Optionally: trigger order fulfillment, notifications, etc.
            return redirect()->route('orders.show', $order->id)->with('success', 'Payment successful!');
        }
        return redirect()->route('orders.show', $order->id)->with('error', 'Payment verification failed.');
    }
}
