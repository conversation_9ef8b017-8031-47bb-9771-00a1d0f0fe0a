<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig;

class ProfileController extends Controller
{
    /**
     * Display the vendor profile edit form
     */
    public function edit()
    {
        // Get the authenticated vendor
        $vendor = auth()->user()->vendor;
        
        // If vendor not found, redirect back with error
        if (!$vendor) {
            return redirect()->back()->with('error', 'Vendor profile not found.');
        }
        
        return view('vendor.profile.edit', compact('vendor'));
    }
    
    /**
     * Update the vendor profile
     */
    public function update(Request $request)
    {
        // Validate the request data
        $request->validate([
            'business_name' => 'required|string|max:255',
            'phone' => 'required|string|max:15',
            'address_line1' => 'required|string|max:255',
            'address_line2' => 'nullable|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'country' => 'required|string|max:100',
            'logo' => 'nullable|image|mimes:jpg,jpeg,png,svg|max:2048',
            'business_description' => 'nullable|string',
            'social_facebook' => 'nullable|url',
            'social_twitter' => 'nullable|url',
            'social_instagram' => 'nullable|url',
        ]);
        
        // Get the authenticated vendor
        $vendor = auth()->user()->vendor;
        
        if (!$vendor) {
            return redirect()->back()->with('error', 'Vendor profile not found.');
        }
        
        try {
            // Process uploaded logo if provided
            if ($request->hasFile('logo')) {
                // Delete old logo if exists
                if ($vendor->logo) {
                    // Extract the path after the storage directory
                    $oldLogoPath = str_replace('/storage/', '', $vendor->logo);
                    Storage::delete('public/' . $oldLogoPath);
                }
                
                // Store new logo
                $logoPath = $request->file('logo')->store('public/vendor/logos');
                $logoUrl = Storage::url($logoPath);
            }
            
            // Update the vendor profile
            $vendor->update([
                'business_name' => $request->business_name,
                'address_line1' => $request->address_line1,
                'address_line2' => $request->address_line2,
                'city' => $request->city,
                'state' => $request->state,
                'country' => $request->country,
                'contact_phone' => $request->phone,
                'business_description' => $request->business_description,
                'social_facebook' => $request->social_facebook,
                'social_twitter' => $request->social_twitter,
                'social_instagram' => $request->social_instagram,
                // Only update logo if a new one was uploaded
                'logo' => $request->hasFile('logo') ? $logoUrl : $vendor->logo,
            ]);
            
            // Redirect back with success message
            return redirect()->back()->with('success', 'Your profile has been updated successfully!');
        } catch (FileDoesNotExist|FileIsTooBig $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'There was an issue uploading your logo. Please check file type and size and try again.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'An unexpected error occurred while updating your profile. Please try again.');
        }
    }
}
