<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->foreignId('vendor_id')->constrained()->onDelete('cascade'); // Assuming direct link to vendor
            $table->foreignId('product_id')->constrained()->onDelete('cascade'); // Or set null on delete if product can be deleted but order item remains for history

            $table->string('product_name'); // Snapshot of product name
            $table->integer('quantity');
            $table->decimal('unit_price', 10, 2); // Price per unit at time of purchase
            $table->decimal('subtotal', 10, 2);   // quantity * unit_price

            $table->string('status')->default('pending'); // e.g., pending, processing, shipped, delivered, cancelled

            $table->decimal('commission_rate_snapshot', 5, 2)->nullable(); // e.g., 2.70 for 2.7%
            $table->decimal('commission_amount_calculated', 10, 2)->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
};