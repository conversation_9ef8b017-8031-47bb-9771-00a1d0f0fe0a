<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class IsVendor
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        $vendor = $user->vendor;

        if (!$vendor) {
            return redirect()->route('home')->with('error', 'Access denied. Vendor account required.');
        }

        if ($vendor->subscription_status !== 'active') {
            return redirect()->route('vendor.subscription')->with('error', 'Please activate your subscription to continue.');
        }

        if (!$vendor->is_approved) {
            return redirect()->route('vendor.pending')->with('error', 'Your vendor account is pending approval.');
        }

        return $next($request);
    }
}
