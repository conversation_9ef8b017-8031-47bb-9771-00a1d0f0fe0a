@extends('layouts.app')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-dark text-white">
                    <h4 class="mb-0">Become a Vendor</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('vendor.register') }}">
                        @csrf
                        
                        <!-- User Account Information -->
                        <h5 class="mb-3">Account Information</h5>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">Full Name</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                       id="email" name="email" value="{{ old('email') }}" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                       id="password" name="password" required>
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="password_confirmation" class="form-label">Confirm Password</label>
                                <input type="password" class="form-control" 
                                       id="password_confirmation" name="password_confirmation" required>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <!-- Business Information -->
                        <h5 class="mb-3">Business Information</h5>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="business_name" class="form-label">Business Name</label>
                                <input type="text" class="form-control @error('business_name') is-invalid @enderror" 
                                       id="business_name" name="business_name" value="{{ old('business_name') }}" required>
                                @error('business_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="contact_phone" class="form-label">Business Phone Number</label>
                                <input type="tel" class="form-control @error('contact_phone') is-invalid @enderror" 
                                       id="contact_phone" name="contact_phone" value="{{ old('contact_phone') }}" 
                                       placeholder="+234XXXXXXXXXX" required>
                                @error('contact_phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="contact_email_business" class="form-label">Business Email</label>
                            <input type="email" class="form-control @error('contact_email_business') is-invalid @enderror" 
                                   id="contact_email_business" name="contact_email_business" 
                                   value="{{ old('contact_email_business') }}" required>
                            @error('contact_email_business')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="address_line1" class="form-label">Business Address</label>
                            <input type="text" class="form-control @error('address_line1') is-invalid @enderror" 
                                   id="address_line1" name="address_line1" value="{{ old('address_line1') }}" required>
                            @error('address_line1')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="city" class="form-label">City</label>
                                <input type="text" class="form-control @error('city') is-invalid @enderror" 
                                       id="city" name="city" value="{{ old('city') }}" required>
                                @error('city')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4">
                                <label for="state" class="form-label">State</label>
                                <select class="form-select @error('state') is-invalid @enderror" 
                                        id="state" name="state" required onchange="updateDeliveryInfo()">
                                    <option value="">Select State</option>
                                    @php
                                        $states = ['Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue', 
                                                  'Borno', 'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu', 
                                                  'Gombe', 'Imo', 'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi', 
                                                  'Kogi', 'Kwara', 'Lagos', 'Nasarawa', 'Niger', 'Ogun', 'Ondo', 
                                                  'Osun', 'Oyo', 'Plateau', 'Rivers', 'Sokoto', 'Taraba', 'Yobe', 
                                                  'Zamfara', 'Abuja', 'Ibadan', 'Akure'];
                                        $platformStates = ['Lagos', 'Abuja', 'Ibadan', 'Akure'];
                                    @endphp
                                    @foreach($states as $state)
                                        <option value="{{ $state }}" {{ old('state') == $state ? 'selected' : '' }}>
                                            {{ $state }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('state')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4">
                                <label for="postal_code" class="form-label">Postal Code</label>
                                <input type="text" class="form-control @error('postal_code') is-invalid @enderror" 
                                       id="postal_code" name="postal_code" value="{{ old('postal_code') }}">
                                @error('postal_code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <!-- Delivery Zone Information -->
                        <div id="deliveryInfo" class="alert alert-info" style="display: none;">
                            <h6 class="alert-heading">Delivery Zone Information</h6>
                            <div id="deliveryZoneContent"></div>
                        </div>
                        
                        <!-- Subscription Information -->
                        <div class="card bg-light mb-4">
                            <div class="card-body">
                                <h5 class="card-title">Subscription Plans</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card border-primary">
                                            <div class="card-body">
                                                <h6 class="card-title text-primary">Platform Delivery Zone</h6>
                                                <p class="card-text">
                                                    <strong>₦10,000/month</strong><br>
                                                    <small>For vendors in Lagos, Abuja, Ibadan, and Akure</small><br>
                                                    <small>Platform handles delivery on Mon, Wed, Fri</small>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6 class="card-title">Other States Zone</h6>
                                                <p class="card-text">
                                                    <strong>₦7,000/month</strong><br>
                                                    <small>For vendors in other states</small><br>
                                                    <small>You handle your own delivery</small>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="#" target="_blank">Terms and Conditions</a> and 
                                <a href="#" target="_blank">Vendor Agreement</a>
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-dark btn-lg">
                                Register as Vendor
                            </button>
                        </div>
                    </form>
                    
                    <div class="text-center mt-3">
                        <p>Already have a vendor account? <a href="{{ route('login') }}">Login here</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateDeliveryInfo() {
    const state = document.getElementById('state').value;
    const platformStates = ['Lagos', 'Abuja', 'Ibadan', 'Akure'];
    const deliveryInfo = document.getElementById('deliveryInfo');
    const deliveryContent = document.getElementById('deliveryZoneContent');
    
    if (state) {
        deliveryInfo.style.display = 'block';
        
        if (platformStates.includes(state)) {
            deliveryContent.innerHTML = `
                <p class="mb-0"><strong>Platform Delivery Zone</strong></p>
                <p class="mb-0">✓ Platform will handle delivery for your products</p>
                <p class="mb-0">✓ Delivery days: Monday, Wednesday, Friday</p>
                <p class="mb-0">✓ Subscription fee: <strong>₦10,000/month</strong></p>
            `;
        } else {
            deliveryContent.innerHTML = `
                <p class="mb-0"><strong>Other States Zone</strong></p>
                <p class="mb-0">✓ You will handle your own product delivery</p>
                <p class="mb-0">✓ Full control over delivery schedule</p>
                <p class="mb-0">✓ Subscription fee: <strong>₦7,000/month</strong></p>
            `;
        }
    } else {
        deliveryInfo.style.display = 'none';
    }
}

// Check on page load if state is already selected
document.addEventListener('DOMContentLoaded', function() {
    updateDeliveryInfo();
});
</script>
@endsection