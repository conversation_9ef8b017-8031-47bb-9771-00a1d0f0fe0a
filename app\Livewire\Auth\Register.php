<?php

namespace App\Livewire\Auth;

use App\Models\User;
use App\Models\Role;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('components.layouts.auth-bootstrap')]
class Register extends Component
{
    public string $name = '';

    public string $email = '';

    public string $password = '';

    public string $password_confirmation = '';

        public $registering = false;

    /**
     * Handle an incoming registration request.
     */
    public function register(): void
    {
        $this->registering = true;
        
        try {
            $validated = $this->validate([
                'name' => ['required', 'string', 'max:255'],
                'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
                'password' => ['required', 'string', 'confirmed', Rules\Password::defaults()],
            ]);

            $validated['password'] = Hash::make($validated['password']);
            
            // Assign 'customer' role directly as a string
            $validated['role'] = 'customer';

            $user = User::create([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'password' => $validated['password'],
                'role' => $validated['role'] // Store role as string
            ]);

            event(new Registered($user));

            Auth::login($user);
            
            session()->flash('success', 'Registration successful! Welcome to Brandify.');
            
        } catch (\Exception $e) {
            session()->flash('error', 'Registration failed: ' . $e->getMessage());
            $this->registering = false;
            return;
        }

        $this->registering = false;

        $this->redirect(route('dashboard', absolute: false), navigate: true);
    }
}
