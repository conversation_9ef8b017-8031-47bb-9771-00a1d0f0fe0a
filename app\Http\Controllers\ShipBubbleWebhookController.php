<?php

namespace App\Http\Controllers;

use App\Services\ShipBubbleService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ShipBubbleWebhookController extends Controller
{
    protected $shipBubbleService;

    public function __construct(ShipBubbleService $shipBubbleService)
    {
        $this->shipBubbleService = $shipBubbleService;
    }

    /**
     * Handle ShipBubble webhook
     */
    public function handle(Request $request)
    {
        // Get the signature from headers
        $signature = $request->header('x-shipbubble-signature');
        
        if (!$signature) {
            Log::warning('ShipBubble webhook received without signature');
            return response()->json(['error' => 'No signature'], 400);
        }

        // Get the raw payload
        $payload = $request->getContent();
        
        // Verify the signature
        if (!$this->shipBubbleService->verifyWebhookSignature($signature, $payload)) {
            Log::warning('ShipBubble webhook signature verification failed');
            return response()->json(['error' => 'Invalid signature'], 400);
        }

        // Process the webhook
        $data = json_decode($payload, true);
        
        try {
            $result = $this->shipBubbleService->handleWebhook($data);
            
            if ($result) {
                return response()->json(['status' => 'success'], 200);
            } else {
                return response()->json(['status' => 'failed'], 400);
            }
        } catch (\Exception $e) {
            Log::error('ShipBubble webhook processing error: ' . $e->getMessage());
            return response()->json(['error' => 'Processing failed'], 500);
        }
    }
}