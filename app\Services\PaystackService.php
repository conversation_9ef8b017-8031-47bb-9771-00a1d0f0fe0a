<?php
namespace App\Services;

use App\Models\Order;
use App\Models\Vendor;
use App\Models\Commission;
use App\Models\Subscription;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PaystackService
{
    protected $baseUrl;
    protected $secretKey;
    protected $commissionRate = 2.7; // 2.7% platform commission

    public function __construct()
    {
        $this->baseUrl = 'https://api.paystack.co';
        $this->secretKey = config('services.paystack.secret');
    }

    /**
     * Initiate a payment (returns authorization URL and reference)
     */
    public function initializePayment(array $data)
    {
        $response = Http::withToken($this->secretKey)
            ->post($this->baseUrl . '/transaction/initialize', $data);
        return $response->json();
    }

    /**
     * Initialize subscription payment for vendor
     */
    public function initializeSubscriptionPayment($vendor)
    {
        $amount = $vendor->subscription_fee * 100; // Convert to kobo
        
        $data = [
            'email' => $vendor->contact_email_business,
            'amount' => $amount,
            'callback_url' => route('vendor.subscription.callback'),
            'metadata' => [
                'vendor_id' => $vendor->id,
                'payment_type' => 'subscription',
                'amount' => $amount,
            ]
        ];

        return $this->initializePayment($data);
    }

    /**
     * Initialize order payment
     */
    public function initializeOrderPayment($order)
    {
        $data = [
            'email' => $order->customer_email,
            'amount' => $order->total_amount * 100, // Convert to kobo
            'callback_url' => route('order.payment.callback'),
            'metadata' => [
                'order_id' => $order->id,
                'payment_type' => 'order',
                'amount' => $order->total_amount * 100,
            ]
        ];

        return $this->initializePayment($data);
    }

    /**
     * Verify a payment by reference
     */
    public function verifyPayment($reference)
    {
        $response = Http::withToken($this->secretKey)
            ->get($this->baseUrl . '/transaction/verify/' . $reference);
        return $response->json();
    }

    /**
     * Handle webhook events from Paystack
     */
    public function handleWebhook($payload)
    {
        try {
            $event = $payload['event'];
            $data = $payload['data'];
            
            if ($event === 'charge.success') {
                $metadata = $data['metadata'];
                $paymentType = $metadata['payment_type'] ?? null;
                
                switch ($paymentType) {
                    case 'subscription':
                        return $this->handleSubscriptionPayment($data);
                    case 'order':
                        return $this->handleOrderPayment($data);
                    default:
                        Log::warning('Unknown payment type in webhook', ['metadata' => $metadata]);
                        return false;
                }
            }
            
            return true;
        } catch (\Exception $e) {
            Log::error('Paystack webhook error', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);
            return false;
        }
    }

    /**
     * Handle successful subscription payment
     */
    protected function handleSubscriptionPayment($data)
    {
        $vendorId = $data['metadata']['vendor_id'];
        $vendor = Vendor::findOrFail($vendorId);
        
        // Create or update subscription
        $subscription = Subscription::updateOrCreate(
            ['vendor_id' => $vendorId],
            [
                'plan_name' => $vendor->isPlatformDeliveryZone() ? 'platform_zone' : 'other_zone',
                'amount_paid' => $data['amount'] / 100, // Convert from kobo
                'start_date' => now(),
                'end_date' => now()->addMonth(),
                'status' => 'active',
                'paystack_reference' => $data['reference'],
            ]
        );

        // Update vendor subscription status
        $vendor->update([
            'subscription_status' => 'active',
            'subscription_expires_at' => now()->addMonth(),
            'last_subscription_payment_at' => now(),
        ]);

        return true;
    }

    /**
     * Handle successful order payment
     */
    protected function handleOrderPayment($data)
    {
        $orderId = $data['metadata']['order_id'];
        $order = Order::with('items.product.vendor')->findOrFail($orderId);
        
        // Update order status
        $order->update([
            'payment_status' => 'paid',
            'payment_reference' => $data['reference'],
            'status' => 'processing'
        ]);

        // Calculate and store commission details on OrderItems
        $order->calculateCommissions();
        // Now $order->items will have commission_rate_snapshot and commission_amount_calculated populated.
        // Reload the items relationship to get the updated values if necessary for subsequent logic.
        $order->load('items');


        // Create Commission records for vendor payouts
        foreach ($order->items as $item) {
            // Ensure item has vendor_id and calculated commission before creating Commission record
            if ($item->vendor_id && isset($item->commission_amount_calculated)) {
                Commission::create([ // Using ::create for mass assignment if $fillable is set in Commission model
                    'order_id' => $order->id,
                    'order_item_id' => $item->id, // Good to link to the specific item
                    'vendor_id' => $item->vendor_id,
                    'amount' => $item->commission_amount_calculated,
                    'status' => 'pending_payout' // More descriptive status
                ]);
            }
            
            // Decrement product stock
            if ($item->product) {
                $item->product->decrement('stock_quantity', $item->quantity);
            }
        }
        
        // Trigger ShipBubbles for platform delivery items
        $this->triggerShipBubbles($order);
        
        // Send notifications
        $this->sendOrderNotifications($order);

        return true;
    }
    
    /**
     * Trigger ShipBubbles integration for platform delivery items
     */
    protected function triggerShipBubbles($order)
    {
        try {
            $shipBubbleService = new \App\Services\ShipBubbleService();
            $shipment = $shipBubbleService->createShipment($order);
            
            if ($shipment) {
                Log::info('ShipBubbles shipment created', [
                    'order_id' => $order->id,
                    'shipment_id' => $shipment->id
                ]);
            }
        } catch (\Exception $e) {
            Log::error('ShipBubbles integration failed', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Send order notifications to customer and vendors
     */
    protected function sendOrderNotifications($order)
    {
        // TODO: Implement email notifications
        // This would typically dispatch jobs to send emails
        Log::info('Order notifications should be sent', ['order_id' => $order->id]);
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature($signature, $payload)
    {
        $secretKey = config('services.paystack.webhook_secret');
        $computedSignature = hash_hmac('sha512', $payload, $secretKey);
        
        return hash_equals($signature, $computedSignature);
    }
}
