<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id',
        'vendor_id',
        'order_number',
        'total',
        'status',
        'payment_status',
        'payment_method',
        'payment_reference',
        'shipment_id',
        'tracking_number',
        'shipping_address',
        'shipping_name',
        'shipping_city',
        'shipping_state',
        'shipping_postal_code',
        'shipping_country',
        'shipping_phone',
        'shipping_method',
        'billing_address',
        'delivered_at',
        'customer_name',
        'customer_phone',
        'customer_email',
        'delivery_notes',
    ];
    
    protected $casts = [
        'shipped_at' => 'datetime',
        'delivered_at' => 'datetime',
    ];
    
    /**
     * Boot function to handle order events
     */
    protected static function boot()
    {
        parent::boot();
        
        // Auto-generate order number when creating a new order
        static::creating(function ($order) {
            if (empty($order->order_number)) {
                $order->order_number = 'ORD-' . uniqid();
            }
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }
    
    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }
    
    public function payment()
    {
        return $this->hasOne(Payment::class);
    }

    public function shipment()
    {
        return $this->hasOne(Shipment::class);
    }

    /**
     * Get the tracking URL for this order's shipment
     */
    public function getTrackingUrlAttribute()
    {
        return $this->shipment ? $this->shipment->tracking_url : null;
    }

    /**
     * Get the delivery timeline for this order
     */
    public function getTimelineAttribute()
    {
        return $this->shipment ? $this->shipment->timeline : [];
    }

    /**
     * Get the estimated delivery date
     */
    public function getEstimatedDeliveryDateAttribute()
    {
        return $this->shipment ? $this->shipment->estimated_delivery_date : null;
    }

    /**
     * Check if the order requires platform delivery
     */
    public function requiresPlatformDelivery()
    {
        return $this->orderItems()
            ->whereHas('product', function ($query) {
                $query->where('requires_platform_delivery', true);
            })
            ->exists();
    }

    /**
     * Check if the order is eligible for shipping
     */
    public function isEligibleForShipping()
    {
        return $this->isPaid() && 
               $this->requiresPlatformDelivery() && 
               !$this->shipment_id;
    }
    
    public function isPending()
    {
        return $this->status === 'pending';
    }
    
    public function isProcessing()
    {
        return $this->status === 'processing';
    }
    
    public function isCompleted()
    {
        return $this->status === 'completed';
    }
    
    public function isCancelled()
    {
        return $this->status === 'cancelled';
    }
    
    public function isPaid()
    {
        return $this->payment_status === 'paid';
    }
    
    // Commission calculation for this order
    public function calculateCommissions()
    {
        $commissionRate = config('brandify.commission_rate', 2.7);
        foreach ($this->orderItems as $item) {
            $commissionAmount = round($item->price * $item->quantity * ($commissionRate / 100), 2);
            $item->update([
                'commission_rate_snapshot' => $commissionRate,
                'commission_amount_calculated' => $commissionAmount,
            ]);
        }
    }
}
