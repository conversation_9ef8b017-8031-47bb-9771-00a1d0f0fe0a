<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Shipment;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ShipBubbleService
{
    protected $apiKey;
    protected $baseUrl;

    public function __construct()
    {
        $this->apiKey = config('services.shipbubble.api_key');
        $this->baseUrl = config('services.shipbubble.base_url');
    }

    /**
     * Create a shipment for an order
     */
    public function createShipment(Order $order)
    {
        try {
            // Only create shipment for platform delivery items
            $platformItems = $order->items()->whereHas('product', function ($query) {
                $query->where('requires_platform_delivery', true);
            })->get();

            if ($platformItems->isEmpty()) {
                return null; // No platform delivery items
            }

            $shipmentData = $this->prepareShipmentData($order, $platformItems);
            
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/shipments', $shipmentData);

            if ($response->successful()) {
                $responseData = $response->json();
                
                // Create shipment record
                $shipment = Shipment::create([
                    'order_id' => $order->id,
                    'shipbubble_shipment_id' => $responseData['data']['id'],
                    'tracking_number' => $responseData['data']['tracking_number'],
                    'status' => 'created',
                    'pickup_address' => json_encode($shipmentData['pickup_address']),
                    'delivery_address' => json_encode($shipmentData['delivery_address']),
                    'items' => json_encode($shipmentData['items']),
                    'shipbubble_response' => json_encode($responseData),
                ]);

                // Update order with shipment info
                $order->update([
                    'shipment_id' => $shipment->id,
                    'tracking_number' => $responseData['data']['tracking_number'],
                    'status' => 'shipped'
                ]);

                return $shipment;
            } else {
                Log::error('ShipBubble API Error', [
                    'order_id' => $order->id,
                    'response' => $response->json(),
                    'status' => $response->status()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('ShipBubble Service Error', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Prepare shipment data for ShipBubble API
     */
    protected function prepareShipmentData(Order $order, $items)
    {
        // Get vendor address (pickup location)
        $vendor = $items->first()->vendor;
        
        return [
            'pickup_address' => [
                'name' => $vendor->business_name,
                'phone' => $vendor->contact_phone,
                'email' => $vendor->contact_email_business,
                'address' => $vendor->address_line1,
                'city' => $vendor->city,
                'state' => $vendor->state,
                'country' => 'Nigeria',
            ],
            'delivery_address' => [
                'name' => $order->customer_name,
                'phone' => $order->customer_phone,
                'email' => $order->customer_email,
                'address' => $order->shipping_address,
                'city' => $order->shipping_city,
                'state' => $order->shipping_state,
                'country' => 'Nigeria',
            ],
            'items' => $items->map(function ($item) {
                return [
                    'name' => $item->product->name,
                    'quantity' => $item->quantity,
                    'value' => $item->price,
                    'weight' => $item->product->weight ?? 1, // Default 1kg if not set
                    'description' => $item->product->description,
                ];
            })->toArray(),
            'package' => [
                'weight' => $items->sum(function ($item) {
                    return ($item->product->weight ?? 1) * $item->quantity;
                }),
                'length' => 30, // Default dimensions in cm
                'width' => 20,
                'height' => 10,
            ],
            'delivery_type' => 'standard',
            'payment_type' => 'prepaid',
            'reference' => 'ORD-' . $order->id . '-' . time(),
        ];
    }

    /**
     * Track a shipment
     */
    public function trackShipment($trackingNumber)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->baseUrl . '/shipments/track/' . $trackingNumber);

            if ($response->successful()) {
                return $response->json();
            }

            return null;
        } catch (\Exception $e) {
            Log::error('ShipBubble Tracking Error', [
                'tracking_number' => $trackingNumber,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Handle webhook from ShipBubble
     */
    public function handleWebhook($payload)
    {
        try {
            $event = $payload['event'];
            $data = $payload['data'];

            switch ($event) {
                case 'shipment.status_updated':
                    return $this->handleStatusUpdate($data);
                case 'shipment.delivered':
                    return $this->handleDelivery($data);
                default:
                    Log::info('Unknown ShipBubble webhook event', ['event' => $event]);
                    return true;
            }
        } catch (\Exception $e) {
            Log::error('ShipBubble webhook error', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);
            return false;
        }
    }

    /**
     * Handle shipment status update
     */
    protected function handleStatusUpdate($data)
    {
        $shipment = Shipment::where('shipbubble_shipment_id', $data['id'])->first();
        
        if ($shipment) {
            $shipment->update([
                'status' => $data['status'],
                'last_updated_at' => now(),
            ]);

            // Update order status based on shipment status
            $orderStatus = $this->mapShipmentStatusToOrderStatus($data['status']);
            if ($orderStatus) {
                $shipment->order->update(['status' => $orderStatus]);
            }

            return true;
        }

        return false;
    }

    /**
     * Handle delivery confirmation
     */
    protected function handleDelivery($data)
    {
        $shipment = Shipment::where('shipbubble_shipment_id', $data['id'])->first();
        
        if ($shipment) {
            $shipment->update([
                'status' => 'delivered',
                'delivered_at' => now(),
            ]);

            $shipment->order->update([
                'status' => 'delivered',
                'delivered_at' => now(),
            ]);

            return true;
        }

        return false;
    }

    /**
     * Map ShipBubble status to order status
     */
    protected function mapShipmentStatusToOrderStatus($shipmentStatus)
    {
        $statusMap = [
            'created' => 'shipped',
            'picked_up' => 'in_transit',
            'in_transit' => 'in_transit',
            'out_for_delivery' => 'out_for_delivery',
            'delivered' => 'delivered',
            'failed' => 'delivery_failed',
            'returned' => 'returned',
        ];

        return $statusMap[$shipmentStatus] ?? null;
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature($signature, $payload)
    {
        $secretKey = config('services.shipbubble.webhook_secret');
        $computedSignature = hash_hmac('sha256', $payload, $secretKey);
        
        return hash_equals($signature, $computedSignature);
    }
}
