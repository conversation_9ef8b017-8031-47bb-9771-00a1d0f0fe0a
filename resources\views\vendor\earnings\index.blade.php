@extends('layouts.vendor')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Earnings & Commissions</h2>
                <div>
                    <form method="GET" action="{{ route('vendor.earnings.index') }}" class="d-inline">
                        <select name="period" class="form-select form-select-sm" onchange="this.form.submit()">
                            <option value="week" {{ request('period') == 'week' ? 'selected' : '' }}>This Week</option>
                            <option value="month" {{ request('period', 'month') == 'month' ? 'selected' : '' }}>This Month</option>
                            <option value="year" {{ request('period') == 'year' ? 'selected' : '' }}>This Year</option>
                        </select>
                    </form>
                </div>
            </div>
            
            <!-- Earnings Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="text-muted mb-2">Total Sales</h6>
                            <h3 class="mb-0">₦{{ number_format($totalSales ?? 0, 2) }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="text-muted mb-2">Platform Commission (2.7%)</h6>
                            <h3 class="mb-0 text-danger">-₦{{ number_format($totalCommissions ?? 0, 2) }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="text-muted mb-2">Net Earnings</h6>
                            <h3 class="mb-0 text-success">₦{{ number_format($totalEarnings ?? 0, 2) }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="text-muted mb-2">Available for Withdrawal</h6>
                            <h3 class="mb-0 text-primary">₦{{ number_format($pendingCommissions ?? 0, 2) }}</h3>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Withdrawal Section -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Request Withdrawal</h5>
                        </div>
                        <div class="card-body">
                            @if(($pendingCommissions ?? 0) > 0)
                                <form method="POST" action="{{ route('vendor.earnings.withdraw') }}">
                                    @csrf
                                    <div class="mb-3">
                                        <label for="amount" class="form-label">Withdrawal Amount</label>
                                        <div class="input-group">
                                            <span class="input-group-text">₦</span>
                                            <input type="number" class="form-control @error('amount') is-invalid @enderror" 
                                                   id="amount" name="amount" step="0.01" 
                                                   max="{{ $pendingCommissions ?? 0 }}" required>
                                            @error('amount')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <small class="text-muted">
                                            Maximum: ₦{{ number_format($pendingCommissions ?? 0, 2) }}
                                        </small>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="notes" class="form-label">Notes (Optional)</label>
                                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary">
                                        Request Withdrawal
                                    </button>
                                </form>
                            @else
                                <p class="text-muted mb-0">No funds available for withdrawal.</p>
                            @endif
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Bank Account Details</h5>
                        </div>
                        <div class="card-body">
                            @if($vendor->bank_account_details)
                                @php
                                    $bankDetails = is_string($vendor->bank_account_details) 
                                        ? json_decode($vendor->bank_account_details, true) 
                                        : $vendor->bank_account_details;
                                @endphp
                                <p><strong>Bank Name:</strong> {{ $bankDetails['bank_name'] ?? 'Not set' }}</p>
                                <p><strong>Account Name:</strong> {{ $bankDetails['account_name'] ?? 'Not set' }}</p>
                                <p><strong>Account Number:</strong> {{ $bankDetails['account_number'] ?? 'Not set' }}</p>
                                <a href="{{ route('vendor.profile.edit') }}" class="btn btn-sm btn-outline-primary">
                                    Update Bank Details
                                </a>
                            @else
                                <p class="text-muted">Please update your bank account details to receive withdrawals.</p>
                                <a href="{{ route('vendor.profile.edit') }}" class="btn btn-primary">
                                    Add Bank Details
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Transactions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recent Transactions</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Order #</th>
                                    <th>Sale Amount</th>
                                    <th>Commission (2.7%)</th>
                                    <th>Net Earning</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($recentTransactions ?? [] as $transaction)
                                    <tr>
                                        <td>{{ $transaction->created_at->format('M d, Y') }}</td>
                                        <td>#{{ $transaction->order_id }}</td>
                                        <td>₦{{ number_format($transaction->sale_amount, 2) }}</td>
                                        <td class="text-danger">-₦{{ number_format($transaction->commission_amount, 2) }}</td>
                                        <td class="text-success">₦{{ number_format($transaction->net_amount, 2) }}</td>
                                        <td>
                                            @if($transaction->status === 'paid')
                                                <span class="badge bg-success">Paid</span>
                                            @elseif($transaction->status === 'pending')
                                                <span class="badge bg-warning">Pending</span>
                                            @else
                                                <span class="badge bg-secondary">{{ ucfirst($transaction->status) }}</span>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center text-muted">No transactions found</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Withdrawal History -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Withdrawal History</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Date Requested</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Processed Date</th>
                                    <th>Reference</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($withdrawals ?? [] as $withdrawal)
                                    <tr>
                                        <td>{{ $withdrawal->created_at->format('M d, Y') }}</td>
                                        <td>₦{{ number_format($withdrawal->amount, 2) }}</td>
                                        <td>
                                            @if($withdrawal->status === 'completed')
                                                <span class="badge bg-success">Completed</span>
                                            @elseif($withdrawal->status === 'pending')
                                                <span class="badge bg-warning">Pending</span>
                                            @elseif($withdrawal->status === 'rejected')
                                                <span class="badge bg-danger">Rejected</span>
                                            @else
                                                <span class="badge bg-secondary">{{ ucfirst($withdrawal->status) }}</span>
                                            @endif
                                        </td>
                                        <td>{{ $withdrawal->processed_at ? $withdrawal->processed_at->format('M d, Y') : '-' }}</td>
                                        <td>{{ $withdrawal->reference ?? '-' }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="text-center text-muted">No withdrawal history</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection