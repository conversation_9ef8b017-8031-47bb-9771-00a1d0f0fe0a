<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    use HasFactory;
    protected $fillable = [
        'order_id',
        'vendor_id',
        'product_id',
        'product_name',
        'quantity',
        // 'price', // Removed as it's redundant with unit_price and subtotal, and not in DB
        'unit_price',
        'subtotal',
        'status',
        'commission_rate_snapshot',
        'commission_amount_calculated',
    ];
    
    /**
     * Boot function to handle model events
     */
    protected static function boot()
    {
        parent::boot();
        
        // Auto-calculate subtotal when saving
        static::saving(function ($orderItem) {
            // unit_price and subtotal are now expected to be set by the controller
            // before creating/saving the OrderItem.
            // if (empty($orderItem->unit_price)) {
            //     $orderItem->unit_price = $orderItem->price; // 'price' is removed
            // }
            //
            // if (empty($orderItem->subtotal)) {
            //    // This logic assumed 'price' was unit_price or was ambiguous.
            //    // Now subtotal is set directly by controller.
            //    // $orderItem->subtotal = $orderItem->unit_price * $orderItem->quantity;
            // }
            
            if (empty($orderItem->product_name) && $orderItem->product) {
                $orderItem->product_name = $orderItem->product->name;
            }
            
            // Commission calculation will be handled by Order::calculateCommissions()
            // to ensure it's done at the correct point in the lifecycle (e.g., after payment).
            // if (empty($orderItem->commission_rate_snapshot)) {
            //     $orderItem->commission_rate_snapshot = config('brandify.commission_rate', 2.7);
            // }
            //
            // if (empty($orderItem->commission_amount_calculated)) {
            //     $orderItem->commission_amount_calculated = ($orderItem->subtotal * $orderItem->commission_rate_snapshot) / 100;
            // }
        });
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
    
    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }
}
