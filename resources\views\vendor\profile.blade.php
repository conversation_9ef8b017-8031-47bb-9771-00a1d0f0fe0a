@extends('layouts.vendor') {{-- Assuming a vendor-specific layout exists --}}

@section('content')
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow-sm">
                    <div class="card-header bg-dark text-white">
                        <h4 class="mb-0">Edit Vendor Profile</h4>
                    </div>
                    <div class="card-body">
                        @if (session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif
                        @if (session('error'))
                            <div class="alert alert-danger">
                                {{ session('error') }}
                            </div>
                        @endif

                        <form method="POST" action="{{ route('vendor.profile.update') }}">
                            @csrf
                            @method('PUT')

                            <h5 class="mb-3">Business Information</h5>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="business_name" class="form-label">Business Name</label>
                                    <input type="text" class="form-control @error('business_name') is-invalid @enderror"
                                        id="business_name" name="business_name"
                                        value="{{ old('business_name', $vendor->business_name ?? '') }}" required>
                                    @error('business_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="contact_phone" class="form-label">Business Phone Number</label>
                                    <input type="tel" class="form-control @error('contact_phone') is-invalid @enderror"
                                        id="contact_phone" name="contact_phone"
                                        value="{{ old('contact_phone', $vendor->contact_phone ?? '') }}"
                                        placeholder="080XXXXXXXX" required>
                                    @error('contact_phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="contact_email_business" class="form-label">Business Email</label>
                                <input type="email"
                                    class="form-control @error('contact_email_business') is-invalid @enderror"
                                    id="contact_email_business" name="contact_email_business"
                                    value="{{ old('contact_email_business', $vendor->contact_email_business ?? '') }}"
                                    required>
                                @error('contact_email_business')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="address_line1" class="form-label">Business Address</label>
                                <input type="text" class="form-control @error('address_line1') is-invalid @enderror"
                                    id="address_line1" name="address_line1"
                                    value="{{ old('address_line1', $vendor->address_line1 ?? '') }}" required>
                                @error('address_line1')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label for="city" class="form-label">City</label>
                                    <input type="text" class="form-control @error('city') is-invalid @enderror"
                                        id="city" name="city" value="{{ old('city', $vendor->city ?? '') }}"
                                        required>
                                    @error('city')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="state" class="form-label">State</label>
                                    <select class="form-select @error('state') is-invalid @enderror" id="state"
                                        name="state" required>
                                        <option value="">Select State</option>
                                        @php
                                            $states = [
                                                'Abia',
                                                'Adamawa',
                                                'Akwa Ibom',
                                                'Anambra',
                                                'Bauchi',
                                                'Bayelsa',
                                                'Benue',
                                                'Borno',
                                                'Cross River',
                                                'Delta',
                                                'Ebonyi',
                                                'Edo',
                                                'Ekiti',
                                                'Enugu',
                                                'Gombe',
                                                'Imo',
                                                'Jigawa',
                                                'Kaduna',
                                                'Kano',
                                                'Katsina',
                                                'Kebbi',
                                                'Kogi',
                                                'Kwara',
                                                'Lagos',
                                                'Nasarawa',
                                                'Niger',
                                                'Ogun',
                                                'Ondo',
                                                'Osun',
                                                'Oyo',
                                                'Plateau',
                                                'Rivers',
                                                'Sokoto',
                                                'Taraba',
                                                'Yobe',
                                                'Zamfara',
                                                'Abuja',
                                                /* Added missing states from register form */ 'Ibadan',
                                                'Akure',
                                            ];
                                        @endphp
                                        @foreach ($states as $stateOption)
                                            <option value="{{ $stateOption }}"
                                                {{ old('state', $vendor->state ?? '') == $stateOption ? 'selected' : '' }}>
                                                {{ $stateOption }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('state')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <hr class="my-4">
                            <h5 class="mb-3">Banking Information (For Payouts)</h5>

                            <div class="mb-3">
                                <label for="bank_name" class="form-label">Bank Name</label>
                                <input type="text" class="form-control @error('bank_name') is-invalid @enderror"
                                    id="bank_name" name="bank_name"
                                    value="{{ old('bank_name', $vendor->bank_name ?? '') }}">
                                @error('bank_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="bank_account_name" class="form-label">Bank Account Name</label>
                                    <input type="text"
                                        class="form-control @error('bank_account_name') is-invalid @enderror"
                                        id="bank_account_name" name="bank_account_name"
                                        value="{{ old('bank_account_name', $vendor->bank_account_name ?? '') }}">
                                    @error('bank_account_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="bank_account_number" class="form-label">Bank Account Number</label>
                                    <input type="text"
                                        class="form-control @error('bank_account_number') is-invalid @enderror"
                                        id="bank_account_number" name="bank_account_number"
                                        value="{{ old('bank_account_number', $vendor->bank_account_number ?? '') }}">
                                    @error('bank_account_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="form-check mb-4">
                                <input class="form-check-input @error('commission_agreement') is-invalid @enderror"
                                    type="checkbox" id="commission_agreement" name="commission_agreement" value="1"
                                    {{ old('commission_agreement', $vendor->has_completed_onboarding ?? false) ? 'checked' : '' }}>
                                <label class="form-check-label" for="commission_agreement">
                                    I understand and agree to the platform's commission structure.
                                </label>
                                @error('commission_agreement')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-dark btn-lg">
                                    Update Profile
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
