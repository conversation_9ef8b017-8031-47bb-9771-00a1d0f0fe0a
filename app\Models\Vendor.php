<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Vendor extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id',
        'business_name',
        'slug',
        'business_registration_number',
        'tax_identification_number',
        'bank_account_details',
        'address_line1',
        'address_line2',
        'city',
        'state',
        'postal_code',
        'contact_phone',
        'contact_email_business',
        'delivery_zone_type',
        'delivery_policy_text',
        'subscription_status',
        'subscription_expires_at',
        'last_subscription_payment_at',
        'is_approved',
    ];

    protected $casts = [
        'bank_account_details' => 'json',
        'subscription_expires_at' => 'datetime',
        'last_subscription_payment_at' => 'datetime',
        'is_approved' => 'boolean',
    ];

    // Nigerian platform delivery states
    protected static $platformDeliveryStates = [
        'Lagos',
        'Abuja',
        'Ibadan',
        'Akure'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function subscription()
    {
        return $this->hasOne(Subscription::class);
    }

    public function getRouteKeyName()
    {
        return 'slug';
    }

    public function getSubscriptionFeeAttribute()
    {
        return $this->isPlatformDeliveryZone() ? 10000 : 7000; // Amount in Naira
    }

    public function isPlatformDeliveryZone()
    {
        return in_array($this->state, self::$platformDeliveryStates);
    }

    public function getDeliveryScheduleAttribute()
    {
        if ($this->isPlatformDeliveryZone()) {
            return [
                'Monday' => true,
                'Wednesday' => true,
                'Friday' => true,
                'Tuesday' => false,
                'Thursday' => false,
                'Saturday' => false,
                'Sunday' => false,
            ];
        }
        
        return null; // Non-platform zones handle their own delivery
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($vendor) {
            // Set delivery zone type based on state
            $vendor->delivery_zone_type = in_array($vendor->state, self::$platformDeliveryStates) 
                ? 'platform_delivery_zone' 
                : 'other_states_zone';

            // Generate slug from business name if not set
            if (!$vendor->slug) {
                $vendor->slug = Str::slug($vendor->business_name);
            }

            // Set default subscription status
            if (!$vendor->subscription_status) {
                $vendor->subscription_status = 'pending_payment';
            }
        });
    }
}
