<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Vendor;
use App\Models\User;

class VendorController extends Controller
{
    public function index()
    {
        $vendors = Vendor::with('user')->paginate(15);
        return view('admin.vendors.index', compact('vendors'));
    }
    public function show(Vendor $vendor)
    {
        return view('admin.vendors.show', compact('vendor'));
    }
    public function edit(Vendor $vendor)
    {
        return view('admin.vendors.edit', compact('vendor'));
    }
    public function update(Request $request, Vendor $vendor)
    {
        $data = $request->validate([
            'business_name' => 'required|string|max:255|unique:vendors,business_name,' . $vendor->id,
            'address_line1' => 'required|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'contact_phone' => 'required|string|regex:/^0[789][01]\d{8}$/',
            'contact_email_business' => 'required|email|max:255',
            'delivery_zone_type' => 'required|in:platform_delivery_zone,other_states_zone',
            'subscription_status' => 'required|in:active,inactive,pending_payment,expired',
            'approved' => 'boolean',
        ]);
        $vendor->update($data);
        return redirect()->route('admin.vendors.index')->with('success', 'Vendor updated successfully.');
    }
    public function destroy(Vendor $vendor)
    {
        $vendor->delete();
        return redirect()->route('admin.vendors.index')->with('success', 'Vendor deleted successfully.');
    }
    public function approve($id)
    {
        $vendor = Vendor::findOrFail($id);
        $vendor->approved = true;
        $vendor->save();
        return redirect()->route('admin.vendors.index')->with('success', 'Vendor approved successfully.');
    }
    public function toggleFeatured($id)
    {
        $vendor = Vendor::findOrFail($id);
        $vendor->is_featured = !$vendor->is_featured;
        $vendor->save();
        
        $status = $vendor->is_featured ? 'featured' : 'unfeatured';
        return redirect()->route('admin.vendors.index')->with('success', "Vendor {$status} successfully.");
    }
    public function reject($id)
    {
        $vendor = Vendor::findOrFail($id);
        $vendor->approved = false;
        $vendor->save();
        return redirect()->route('admin.vendors.index')->with('success', 'Vendor rejected.');
    }
}
