<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Vendor;
use App\Models\Subscription; // Added import
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth; // Added Auth Facade
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Carbon\Carbon; // Added import

class VendorController extends Controller
{
    /**
     * Show the vendor registration form.
     */
    public function register()
    {
        return view('vendor.register');
    }

    /**
     * Handle vendor registration and profile creation.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'business_name' => 'required|string|max:255|unique:vendors,business_name',
            'address_line1' => 'required|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'contact_phone' => 'required|string|regex:/^0[789][01]\d{8}$/', // Nigerian phone
            'contact_email_business' => 'required|email|max:255',
        ]);

        // Determine delivery zone type
        $platformStates = ['Lagos', 'Abuja', 'Ibadan', 'Akure'];
        $deliveryZoneType = in_array($request->state, $platformStates)
            ? 'platform_delivery_zone'
            : 'other_states_zone';
        $subscriptionFee = $deliveryZoneType === 'platform_delivery_zone' ? 10000 : 7000;

        // Create user with vendor role
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => 'vendor',
        ]);

        // Create vendor profile
        $vendor = Vendor::create([
            'user_id' => $user->id,
            'business_name' => $request->business_name,
            'address_line1' => $request->address_line1,
            'city' => $request->city,
            'state' => $request->state,
            'contact_phone' => $request->contact_phone,
            'contact_email_business' => $request->contact_email_business,
            'delivery_zone_type' => $deliveryZoneType,
            'subscription_status' => 'pending_payment',
            'approved' => false,
        ]);

        // Optionally: trigger event to send onboarding email or notify admin

        // Use \Auth facade for login to avoid undefined method error
        Auth::login($user);
        return redirect()->route('vendor.dashboard')->with('success', 'Vendor account created. Please complete your subscription payment.');
    }

    /**
     * Show vendor profile for editing.
     */
    public function edit()
    {
        $user = Auth::user();
        $vendor = $user ? $user->vendor : null;
        return view('vendor.profile', compact('vendor'));
    }

    /**
     * Update vendor profile details.
     */
    public function update(Request $request)
    {
        $user = Auth::user();
        $vendor = $user ? $user->vendor : null;
        if (!$vendor) {
            return redirect()->route('vendor.register')->with('error', 'Vendor profile not found.');
        }

        $rules = [
            'business_name' => 'required|string|max:255|unique:vendors,business_name,' . $vendor->id,
            'address_line1' => 'required|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'contact_phone' => 'required|string|regex:/^0[789][01]\d{8}$/',
            'contact_email_business' => 'required|email|max:255',
            // Banking details - make them nullable if not always required at this stage
            // Or add logic to require them only if not already provided
            'bank_name' => 'nullable|string|max:255',
            'bank_account_name' => 'nullable|string|max:255',
            'bank_account_number' => 'nullable|string|max:20',
            'commission_agreement' => 'nullable|accepted', // 'accepted' implies it must be '1', 'yes', 'on' or true
        ];

        // Only require banking details if they are being provided for the first time
        // or if commission agreement is being checked.
        if ($request->filled('bank_name') || $request->filled('bank_account_name') || $request->filled('bank_account_number') || $request->has('commission_agreement')) {
            $rules['bank_name'] = 'required|string|max:255';
            $rules['bank_account_name'] = 'required|string|max:255';
            $rules['bank_account_number'] = 'required|string|max:20';
            $rules['commission_agreement'] = 'required|accepted';
        }


        $validatedData = $request->validate($rules);

        $updateData = [
            'business_name' => $validatedData['business_name'],
            'address_line1' => $validatedData['address_line1'],
            'city' => $validatedData['city'],
            'state' => $validatedData['state'],
            'contact_phone' => $validatedData['contact_phone'],
            'contact_email_business' => $validatedData['contact_email_business'],
        ];

        if (isset($validatedData['bank_name'])) {
            $updateData['bank_name'] = $validatedData['bank_name'];
        }
        if (isset($validatedData['bank_account_name'])) {
            $updateData['bank_account_name'] = $validatedData['bank_account_name'];
        }
        if (isset($validatedData['bank_account_number'])) {
            $updateData['bank_account_number'] = $validatedData['bank_account_number'];
        }
        
        // Update has_completed_onboarding if commission_agreement is present and accepted
        if (isset($validatedData['commission_agreement']) && $validatedData['commission_agreement']) {
            $updateData['has_completed_onboarding'] = true;
        }


        $vendor->update($updateData);

        return redirect()->route('vendor.profile.edit')->with('success', 'Profile updated successfully.');
    }
    
    /**
     * Display the vendor onboarding page
     */
    public function onboarding()
    {
        /** @var \App\Models\User|null $user */
        $user = Auth::user();
        if (!$user || !$user->isVendor()) {
            return redirect()->route('vendor.register');
        }
        
        $vendor = $user->vendor;
        
        if (!$vendor) {
            // This case might indicate an issue, as a user who isVendor should have a vendor record.
            // Or it could be part of the onboarding flow before the vendor record is fully complete.
            // For now, redirecting to register or dashboard might be appropriate.
            return redirect()->route('vendor.register')->with('error', 'Vendor profile not yet fully set up.');
        }
        
        return view('vendor.onboarding', compact('vendor'));
    }
    
    // Removed completeProfile method as its functionality is merged into update()
    
    /**
     * Display the vendor storefront
     */
    public function show($slug)
    {
        $vendor = Vendor::where('slug', $slug)
            ->where('approved', true)
            ->firstOrFail();
        $products = $vendor->products()
            ->where('is_active', true)
            ->paginate(12);
        return view('vendor.show', compact('vendor', 'products'));
    }

    /**
     * Initiate vendor subscription payment via Paystack
     */
    public function paySubscription(Request $request)
    {
        $user = Auth::user();
        $vendor = $user ? $user->vendor : null;
        if (!$vendor) {
            return redirect()->route('vendor.register')->with('error', 'Vendor profile not found.');
        }
        $amount = $vendor->delivery_zone_type === 'platform_delivery_zone' ? 10000 : 7000;
        $paystack = new \App\Services\PaystackService();
        $data = [
            'email' => $vendor->contact_email_business,
            'amount' => $amount * 100, // Paystack expects kobo
            'metadata' => [
                'vendor_id' => $vendor->id,
                'type' => 'subscription',
            ],
            'callback_url' => route('vendor.subscription.callback'),
        ];
        $response = $paystack->initializePayment($data);
        if (isset($response['status']) && $response['status'] && isset($response['data']['authorization_url'])) {
            return redirect($response['data']['authorization_url']);
        }
        return back()->with('error', 'Unable to initiate payment. Please try again.');
    }

    /**
     * Handle Paystack callback for subscription payment
     */
    public function subscriptionCallback(Request $request)
    {
        $reference = $request->query('reference');
        $paystack = new \App\Services\PaystackService();
        $result = $paystack->verifyPayment($reference);
        if (isset($result['status']) && $result['status'] && isset($result['data']['status']) && $result['data']['status'] === 'success') {
            $user = Auth::user();
            $vendor = $user ? $user->vendor : null;

            if (!$vendor) {
                // This should ideally not happen if the payment was initiated by an authenticated vendor
                return redirect()->route('vendor.dashboard')->with('error', 'Vendor context lost during payment verification.');
            }
            $vendor->update(['subscription_status' => 'active']);

            // Create Subscription record
            $planName = $vendor->delivery_zone_type === 'platform_delivery_zone' ? 'Platform Zone Subscription' : 'Other States Subscription';
            $amountPaid = $result['data']['amount'] / 100; // Amount from Paystack is in kobo

            Subscription::create([
                'vendor_id' => $vendor->id,
                'plan_name' => $planName,
                'amount_paid' => $amountPaid,
                'start_date' => Carbon::now(),
                'end_date' => Carbon::now()->addMonth(), // Assuming 1 month subscription
                'status' => 'active',
                'paystack_reference' => $reference,
            ]);

            // Optionally: send notification, etc.
            return redirect()->route('vendor.dashboard')->with('success', 'Subscription payment successful and recorded!');
        }
        return redirect()->route('vendor.dashboard')->with('error', 'Payment verification failed.');
    }
}
