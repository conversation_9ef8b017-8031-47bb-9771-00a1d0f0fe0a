# Nigerian Brands Multivendor Ecommerce Implementation Plan

## Current State Analysis

### What's Already Implemented ✅

-   Basic Laravel application structure
-   User authentication system with role-based access
-   Basic Vendor model with some required fields
-   Product management with variants, colors, and sizes
-   Order and OrderItem models
-   Basic Paystack integration
-   Commission tracking system
-   Admin dashboard structure
-   Cart functionality
-   Wishlist system
-   Review system

### What Needs Enhancement/Implementation 🔧

## Module 1: Enhanced Vendor Management

### Current Issues to Fix:

1. **Vendor Model**: Missing key fields from gemini.md requirements
2. **Registration Flow**: Needs Nigerian-specific delivery zone logic
3. **Subscription System**: Needs proper tiered pricing based on zones
4. **Approval Workflow**: Missing proper vendor approval process

### Implementation Tasks:

#### 1.1 Update Vendor Model and Migration

```php
// Add missing fields to vendors table:
- slug (for SEO-friendly URLs)
- address_line2 (optional)
- postal_code
- country (default: Nigeria)
- business_registration_number
- tax_identification_number
- bank_account_details (JSON)
- subscription_expires_at
- last_subscription_payment_at
- delivery_policy_text
```

#### 1.2 Implement Nigerian Delivery Zone Logic

```php
// Platform delivery zones:
$platformDeliveryStates = ['Lagos', 'Abuja', 'Ibadan', 'Akure'];
// Delivery schedule: Monday, Wednesday, Friday
// Subscription fees: ₦10,000 (platform zones), ₦7,000 (other zones)
```

#### 1.3 Enhanced Vendor Registration Controller

-   Automatic delivery zone detection based on state
-   Display appropriate subscription fees
-   Collect all required business information
-   Implement proper validation for Nigerian phone numbers
-   Set initial status: `approved = false`, `subscription_status = 'pending_payment'`

#### 1.4 Subscription Management

-   Create subscription payment flow with Paystack
-   Implement subscription expiry checking
-   Add scheduled tasks for subscription management
-   Create subscription renewal notifications

## Module 2: Product Management Enhancement

### Current Issues to Fix:

1. **Missing Commission Logic**: Products need `requires_platform_delivery` field
2. **Image Management**: Implement proper image handling
3. **Product Validation**: Enhanced validation rules

### Implementation Tasks:

#### 2.1 Update Product Model

```php
// Add missing fields:
- requires_platform_delivery (boolean, auto-set based on vendor zone)
- weight (for shipping calculations)
- dimensions (JSON: length, width, height)
- shipping_class
- meta_title, meta_description (SEO)
```

#### 2.2 Commission Calculation System

-   Implement 2.7% platform commission
-   Store commission rate snapshot per order item
-   Create commission reporting for vendors and admin

#### 2.3 Enhanced Product CRUD

-   Policy-based access control
-   Automatic `requires_platform_delivery` setting
-   Image upload with `spatie/laravel-medialibrary`
-   SEO optimization fields

## Module 3: Order Management with Nigerian Integrations

### Current Issues to Fix:

1. **Missing ShipBubbles Integration**: No shipping service integration
2. **Order Status Flow**: Needs Nigerian-specific statuses
3. **Webhook Handling**: Incomplete Paystack webhook processing

### Implementation Tasks:

#### 3.1 Create ShipBubbles Service

```php
// App\Services\ShipBubbleService
- API integration for shipment creation
- Tracking number management
- Delivery status updates
- Error handling and logging
```

#### 3.2 Enhanced Order Flow

```php
// Order statuses for Nigerian market:
- pending_payment
- processing
- awaiting_platform_pickup (for platform delivery)
- shipped
- out_for_delivery
- delivered
- cancelled
- returned
```

#### 3.3 Paystack Webhook Enhancement

-   Complete webhook verification
-   Automatic commission calculation
-   Stock decrement
-   ShipBubbles integration trigger
-   Customer and vendor notifications

## Module 4: Customer Management Enhancement

### Current State: Basic authentication exists

### Enhancements Needed:

#### 4.1 Customer Profile Enhancement

-   Multiple shipping addresses
-   Nigerian-specific address fields
-   Phone number verification
-   Order history with tracking

#### 4.2 Customer Dashboard

-   Order tracking
-   Wishlist management
-   Review management
-   Address book

## Module 5: Search and Filtering System

### Current State: Basic search exists

### Enhancements Needed:

#### 5.1 Advanced Search Implementation

-   Product name, description search
-   Category-based filtering
-   Vendor-based filtering
-   Price range filtering
-   Rating-based filtering
-   Availability filtering

#### 5.2 Search Optimization

-   Implement Laravel Scout (future)
-   Search result caching
-   Search analytics

## Module 6: Rating and Review Enhancement

### Current State: Basic review model exists

### Enhancements Needed:

#### 6.1 Review System Enhancement

-   Purchase verification before review
-   Vendor response functionality
-   Review moderation system
-   Average rating calculation
-   Review helpfulness voting

## Module 7: Admin Dashboard Enhancement

### Current State: Basic admin views exist

### Enhancements Needed:

#### 7.1 Comprehensive Admin Panel

-   Vendor approval workflow
-   Subscription management
-   Commission reporting
-   Order management
-   Customer support tools

#### 7.2 Analytics and Reporting

-   Revenue dashboards
-   Vendor performance metrics
-   Customer analytics
-   Commission reports

## Module 8: Security and Nigerian Compliance

### Implementation Tasks:

#### 8.1 Security Enhancements

-   Rate limiting on sensitive routes
-   CSRF protection
-   Input validation and sanitization
-   SQL injection prevention
-   XSS protection

#### 8.2 Nigerian Compliance

-   NDPR (Nigerian Data Protection Regulation) compliance
-   Privacy policy implementation
-   Data consent management
-   Local payment method integration

## Technical Implementation Priority

### Phase 1: Core Vendor Management (Week 1-2)

1. Fix Vendor model and migrations
2. Implement Nigerian delivery zone logic
3. Enhanced vendor registration
4. Subscription payment system

### Phase 2: Product and Order Enhancement (Week 3-4)

1. Product model enhancements
2. Commission calculation system
3. ShipBubbles integration
4. Enhanced order flow

### Phase 3: Customer Experience (Week 5)

1. Advanced search and filtering
2. Enhanced customer dashboard
3. Review system improvements

### Phase 4: Admin and Analytics (Week 6)

1. Comprehensive admin panel
2. Analytics and reporting
3. Security enhancements

### Phase 5: Compliance and Optimization (Week 7)

1. Nigerian compliance features
2. Performance optimization
3. Testing and bug fixes

## Required Dependencies

### New Packages to Install:

```bash
composer require spatie/laravel-medialibrary  # Image management
composer require spatie/laravel-permission    # Enhanced permissions
composer require laravel/scout               # Search (future)
composer require pusher/pusher-php-server    # Real-time notifications
```

### Environment Variables to Add:

```env
# ShipBubbles Integration
SHIPBUBBLE_API_KEY=your_shipbubble_api_key
SHIPBUBBLE_BASE_URL=https://api.shipbubble.com

# Enhanced Paystack
PAYSTACK_PUBLIC_KEY=your_paystack_public_key
PAYSTACK_SECRET_KEY=your_paystack_secret_key
PAYSTACK_WEBHOOK_SECRET=your_webhook_secret

# Nigerian Specific
DEFAULT_CURRENCY=NGN
PLATFORM_COMMISSION_RATE=2.7
PLATFORM_DELIVERY_STATES=Lagos,Abuja,Ibadan,Akure
```

## Database Schema Updates Required

### New Tables:

1. `shipments` - Track ShipBubbles shipments
2. `vendor_bank_details` - Store vendor banking info
3. `customer_addresses` - Multiple customer addresses
4. `search_logs` - Track search analytics

### Table Updates:

1. `vendors` - Add missing fields per gemini.md
2. `products` - Add shipping and SEO fields
3. `orders` - Add Nigerian-specific fields
4. `users` - Add phone verification fields

## Success Metrics

### Technical Metrics:

-   All 8 modules from gemini.md implemented
-   100% Nigerian market compliance
-   Sub-3 second page load times
-   99.9% uptime

### Business Metrics:

-   Vendor onboarding completion rate > 80%
-   Order completion rate > 95%
-   Customer satisfaction > 4.5/5
-   Platform commission collection > 98%

## Risk Mitigation

### Technical Risks:

1. **ShipBubbles API Integration**: Implement robust error handling and fallback options
2. **Payment Processing**: Ensure Paystack webhook reliability
3. **Performance**: Implement caching and database optimization

### Business Risks:

1. **Vendor Adoption**: Provide comprehensive onboarding support
2. **Customer Trust**: Implement strong security and transparent policies
3. **Regulatory Compliance**: Stay updated with Nigerian e-commerce regulations

## Next Steps

1. **Immediate**: Start with Phase 1 implementation
2. **Week 1**: Complete vendor management enhancements
3. **Week 2**: Begin product and order system improvements
4. **Ongoing**: Regular testing and stakeholder feedback

This plan transforms your current Laravel application into a comprehensive Nigerian multivendor e-commerce platform that meets all requirements specified in gemini.md while leveraging your existing codebase.

---

## Detailed Module Specifications (from gemini.md)

**Project Objective:** Enhance an existing Laravel multivendor e-commerce application targeting the Nigerian market. This involves implementing new features for vendor management, product management, order processing (including Paystack and ShipBubbles integration), customer management, search, reviews, and an admin dashboard. Adhere to Nigerian market specifics (delivery zones, pricing, payment methods) and Laravel best practices.

**General Instructions for Implementation:**
For each module below, assume you are modifying or adding to an existing Laravel application.

1.  **Analyze the Requirement:** Understand the goal of the module.
2.  **Implement in Laravel:** Provide code structures, and where appropriate, example code snippets for:
    -   **Models:** Define attributes, relationships, and necessary fillable/guarded properties. Include migrations.
    -   **Controllers:** Outline methods for CRUD operations and business logic.
    -   **Routes:** Define web routes (e.g., in `routes/web.php`), appropriately grouped and middleware-protected.
    -   **Validation:** Specify validation rules for input data, preferably using Laravel Form Requests.
    -   **Services:** For external API integrations (Paystack, ShipBubbles) or complex business logic, suggest creating service classes (e.g., `App\Services\ShipBubbleService`).
    -   **Middleware/Policies:** For authentication and authorization (e.g., 'isVendor', 'isAdmin', ownership policies).
    -   **Blade Views (Conceptual):** Describe what forms/information views should display.
    -   **Events/Listeners/Queues:** Suggest where these might be useful (e.g., sending emails, processing ShipBubbles after payment).
3.  **External Integrations:**
    -   For Paystack: Detail setup (env keys), payment initiation, and webhook handling.
    -   For ShipBubbles: Detail setup (env keys: `SHIPBUBBLE_API_KEY`), how to structure API calls within a service class (`ShipBubbleService`), what data is needed (pickup, delivery, items, dimensions), and when to trigger shipment creation (after successful payment for platform-delivered items). Assume access to ShipBubbles API documentation for specific endpoint details.
4.  **Database Schema:** Ensure the described models translate into a coherent database schema.

---

### Module 1: Enhanced Vendor Management

-   **Goal:** Robust vendor registration, login, profiles, and tiered subscriptions based on delivery zones.
-   **Tasks:**
    -   **User Role:** Ensure `User` model has a `role` (e.g., 'vendor').
    -   **Vendor Model (`Vendor`):**
        -   Attributes: `user_id` (FK), `business_name`, `address_line1`, `city`, `state`, `contact_phone`, `contact_email_business`, `delivery_zone_type` (ENUM: 'platform_delivery_zone', 'other_states_zone'), `subscription_status` (ENUM: 'active', 'inactive', 'pending_payment', 'expired'), `approved` (BOOLEAN, default false).
        -   Relationships: `User` hasOne `Vendor`, `Vendor` belongsTo `User`.
    -   **Registration:**
        -   Collect vendor details. Determine `delivery_zone_type` based on `state` (Lagos, Abuja, Ibadan, Akure = 'platform_delivery_zone', others = 'other_states_zone').
        -   Display delivery policy: Platform delivery only for 'platform_delivery_zone' states, on Mon, Wed, Fri.
        -   Display subscription fee: 10,000 NGN for 'platform_delivery_zone', 7,000 NGN for 'other_states_zone'.
        -   Initial status: `approved = false`, `subscription_status = 'pending_payment'`.
        -   Validation: Business name, address, state, Nigerian phone.
    -   **Profile Management:** CRUD for vendor-editable profile details.
    -   **Subscription Model (`Subscription`):**
        -   Attributes: `vendor_id`, `plan_name`, `amount_paid`, `start_date`, `end_date`, `status`, `paystack_reference`.
        -   Integrate Paystack for subscription payments. On success, update `vendors.subscription_status` to 'active' and create `Subscription` record.
        -   Scheduled task to check for expired subscriptions.
    -   **Authorization:** Use middleware (`IsVendor`) and Policies.

### Module 2: Product Management with Commission

-   **Goal:** Allow active vendors to manage products; implement platform commission.
-   **Tasks:**
    -   **Product Model (`Product`):**
        -   Attributes: `vendor_id` (FK), `name`, `slug`, `description`, `price` (DECIMAL), `stock_quantity` (INTEGER), `is_active` (BOOLEAN), `requires_platform_delivery` (BOOLEAN, based on vendor's zone).
        -   Consider `spatie/laravel-medialibrary` for images.
        -   Relationships: `Vendor` hasMany `Product`, `Product` belongsTo `Vendor`.
        -   (Optional) `ProductVariation` model if variations are needed.
    -   **Product CRUD:** Vendor-only, policy-protected. Set `requires_platform_delivery` on creation.
    -   **Validation:** Name, description, price > 0, stock >= 0.
    -   **Commission:** Platform earns 2.7% on sales. Store rate in `config/ecommerce.php` or `.env`. Calculate and record per order item.

### Module 3: Order Management (with Paystack & ShipBubbles)

-   **Goal:** Customer order placement, history, tracking. Integrate Paystack and ShipBubbles.
-   **Tasks:**
    -   **Order Models:**
        -   `Order`: `customer_id` (FK to `users`), `order_number`, `total_amount`, `status` (ENUMs), `shipping_address_details`, `payment_gateway_reference`, `shipbubble_shipment_id` (nullable).
        -   `OrderItem`: `order_id`, `product_id`, `vendor_id`, `quantity`, `price_at_purchase`, `commission_rate_snapshot`, `commission_amount_calculated`.
    -   **Cart:** Session/DB-based.
    -   **Checkout:**
        -   Collect shipping info.
        -   Display delivery policy (platform delivery for specific states; vendor handles others).
        -   Paystack integration (e.g., `unicodeveloper/laravel-paystack`): Initiate payment.
    -   **Paystack Webhook:**
        -   Route for `charge.success`. Verify event.
        -   Update `Order` status to 'processing'. Record Paystack reference.
        -   Calculate `commission_amount_calculated` for `OrderItems`.
        -   Decrement product stock.
        -   Send notifications (customer, vendor).
        -   **Trigger ShipBubbles integration.**
    -   **ShipBubbles Integration (`ShipBubbleService`):**
        -   Trigger after successful payment for orders/items where `products.requires_platform_delivery` is true.
        -   `createShipment(Order $order)` method in `ShipBubbleService`.
        -   API Call Data: Vendor address (pickup), customer address (delivery), recipient details, item list (name, qty, value), package dimensions/weight (consider adding these to `Product` model or using defaults).
        -   Store `shipbubble_shipment_id` and tracking number on the `Order` or a dedicated `Shipments` table if multiple shipments per order for different platform-delivery vendors.
        -   Update order status (e.g., 'shipped' or 'awaiting_platform_pickup').
        -   Handle API errors gracefully.
    -   **Order History:** Customer view with status and tracking.

### Module 4: Customer Management

-   **Goal:** Standard customer registration, login, profile.
-   **Tasks:** (Likely leverage existing Laravel auth)
    -   `User` model with `role` ('customer').
    -   Registration (name, email, password). Bcrypt for passwords.
    -   Login.
    -   Profile management (details, addresses).

### Module 5: Search and Filtering

-   **Goal:** Search products by name, category, vendor; filter by price, rating, availability.
-   **Tasks:**
    -   Eloquent-based search: `WHERE LIKE`, `whereHas` for vendor search.
    -   Filtering: Query scopes on `Product` model (e.g., `scopePriceBetween`).
    -   (Future) Consider Laravel Scout.

### Module 6: Rating and Review

-   **Goal:** Customers rate/review products; vendors respond.
-   **Tasks:**
    -   **Review Model (`Review`):**
        -   Attributes: `customer_id`, `product_id`, `vendor_id`, `rating` (1-5), `comment`, `is_approved` (BOOLEAN), `vendor_response` (TEXT).
        -   Relationships: BelongsTo `User`, `Product`, `Vendor`. `Product` hasMany `Review`.
    -   Customers (who purchased) submit reviews.
    -   Vendors respond to reviews on their products.
    -   Display average rating and reviews on product pages.

### Module 7: Admin Dashboard

-   **Goal:** Centralized management for administrators.
-   **Tasks:**
    -   Admin routes (e.g., `/admin`) with `IsAdmin` middleware.
    -   **Management Sections (CRUD interfaces):**
        -   Vendors: List, view, approve, manage subscriptions.
        -   Products: View all, edit, delete, flag.
        -   Orders: View all, filter, update status.
        -   Customers: View, manage accounts.
        -   Reviews: Moderate.
    -   **Analytics/Reporting:**
        -   Dashboard for subscription revenue, platform commission.
        -   Reports: Commission per vendor, sales per vendor.

### Module 8: Security, Best Practices & Compliance (Apply throughout)

-   **Authentication/Authorization:** Laravel Sanctum/web sessions. Gates & Policies.
-   **Input Validation:** Laravel Validator (Form Requests).
-   **Error Handling/Logging:** Laravel's logging, custom exceptions.
-   **Rate Limiting:** Apply to sensitive routes.
-   **Environment Variables (`.env`):** For ALL secrets (DB, Paystack, ShipBubble keys).
-   **SSL/TLS:** Enforce HTTPS in production.
-   **NDPR Compliance:** Consent, data minimization, privacy policy.
-   **Scalability:** Eager loading, query optimization, Laravel Queues (for emails, ShipBubbles calls).

---

## Critical Issues Identified in Current Codebase

### 1. Security Vulnerability - Empty IsVendor Middleware

**File:** `app/Http/Middleware/IsVendor.php`
**Issue:** The middleware is completely empty, allowing unauthorized access to vendor routes.
**Priority:** CRITICAL - Must be fixed immediately

### 2. Missing Vendor Model Fields

**File:** `app/Models/Vendor.php`
**Missing Fields Required by gemini.md:**

-   `delivery_zone_type` (ENUM)
-   `subscription_status` (ENUM)
-   `approved` (BOOLEAN)
-   `business_registration_number`
-   `tax_identification_number`
-   `bank_account_details` (JSON)

### 3. Dashboard Data Inconsistencies

**File:** `resources/views/vendor/dashboard.blade.php`
**Issue:** References `$earnings` data not provided by controller
**File:** `app/Http/Controllers/Vendor/DashboardController.php`
**Fix:** Add earnings calculation and data

### 4. Missing ShipBubbles Integration

**Current State:** No shipping service integration exists
**Required:** Complete ShipBubbles API integration for Nigerian delivery

### 5. Incomplete Commission Automation

**Current State:** Basic commission tracking exists
**Required:** Automated commission calculation and vendor payouts

## Implementation Roadmap

### Week 1: Critical Security and Core Fixes

1. **Day 1-2:** Fix IsVendor middleware (CRITICAL)
2. **Day 3-4:** Update Vendor model with missing fields
3. **Day 5-7:** Implement Nigerian delivery zone logic

### Week 2: Enhanced Vendor Management

1. **Day 1-3:** Complete vendor registration flow
2. **Day 4-5:** Implement subscription payment system
3. **Day 6-7:** Add vendor approval workflow

### Week 3: Product and Order Enhancement

1. **Day 1-3:** Enhance product management with shipping fields
2. **Day 4-5:** Implement commission calculation system
3. **Day 6-7:** Begin ShipBubbles integration

### Week 4: Order Processing and Payments

1. **Day 1-3:** Complete ShipBubbles integration
2. **Day 4-5:** Enhance Paystack webhook handling
3. **Day 6-7:** Implement order tracking system

### Week 5: Customer Experience

1. **Day 1-3:** Advanced search and filtering
2. **Day 4-5:** Enhanced customer dashboard
3. **Day 6-7:** Review system improvements

### Week 6: Admin and Analytics

1. **Day 1-3:** Comprehensive admin panel
2. **Day 4-5:** Analytics and reporting
3. **Day 6-7:** Security enhancements

### Week 7: Compliance and Optimization

1. **Day 1-3:** Nigerian compliance features
2. **Day 4-5:** Performance optimization
3. **Day 6-7:** Testing and bug fixes

This comprehensive plan addresses all requirements from gemini.md while building upon your existing Laravel codebase, ensuring a smooth transformation into a complete Nigerian multivendor e-commerce platform.
