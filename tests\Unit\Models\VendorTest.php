<?php

namespace Tests\Unit\Models;

use Tests\TestCase;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;

class VendorTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_automatically_sets_delivery_zone_type_based_on_state()
    {
        $vendorInLagos = Vendor::factory()->create(['state' => 'Lagos']);
        $vendorInOyo = Vendor::factory()->create(['state' => 'Oyo']);

        $this->assertEquals('platform_delivery_zone', $vendorInLagos->delivery_zone_type);
        $this->assertEquals('other_states_zone', $vendorInOyo->delivery_zone_type);
    }

    /** @test */
    public function it_automatically_generates_slug_from_business_name()
    {
        $vendor = Vendor::factory()->create([
            'business_name' => 'Test Business Name'
        ]);

        $this->assertEquals('test-business-name', $vendor->slug);
    }

    /** @test */
    public function it_sets_correct_subscription_fee_based_on_state()
    {
        $vendorInLagos = Vendor::factory()->create(['state' => 'Lagos']);
        $vendorInOyo = Vendor::factory()->create(['state' => 'Oyo']);

        $this->assertEquals(10000, $vendorInLagos->subscription_fee);
        $this->assertEquals(7000, $vendorInOyo->subscription_fee);
    }

    /** @test */
    public function it_correctly_identifies_platform_delivery_zones()
    {
        $platformStates = ['Lagos', 'Abuja', 'Ibadan', 'Akure'];
        $nonPlatformState = 'Oyo';

        foreach ($platformStates as $state) {
            $vendor = Vendor::factory()->create(['state' => $state]);
            $this->assertTrue($vendor->isPlatformDeliveryZone(), "State {$state} should be a platform delivery zone");
        }

        $vendor = Vendor::factory()->create(['state' => $nonPlatformState]);
        $this->assertFalse($vendor->isPlatformDeliveryZone(), "State {$nonPlatformState} should not be a platform delivery zone");
    }

    /** @test */
    public function it_provides_delivery_schedule_for_platform_zones_only()
    {
        $vendorInLagos = Vendor::factory()->create(['state' => 'Lagos']);
        $vendorInOyo = Vendor::factory()->create(['state' => 'Oyo']);

        $expectedSchedule = [
            'Monday' => true,
            'Wednesday' => true,
            'Friday' => true,
            'Tuesday' => false,
            'Thursday' => false,
            'Saturday' => false,
            'Sunday' => false,
        ];

        $this->assertEquals($expectedSchedule, $vendorInLagos->delivery_schedule);
        $this->assertNull($vendorInOyo->delivery_schedule);
    }

    /** @test */
    public function it_properly_casts_bank_account_details_to_json()
    {
        $bankDetails = [
            'bank_name' => 'Test Bank',
            'account_number' => '**********',
            'account_name' => 'Test Account'
        ];

        $vendor = Vendor::factory()->create([
            'bank_account_details' => $bankDetails
        ]);

        $this->assertIsArray($vendor->bank_account_details);
        $this->assertEquals($bankDetails, $vendor->bank_account_details);

        // Verify it's stored as JSON in the database
        $this->assertDatabaseHas('vendors', [
            'id' => $vendor->id,
            'bank_account_details' => json_encode($bankDetails)
        ]);
    }

    /** @test */
    public function it_properly_casts_datetime_fields()
    {
        $vendor = Vendor::factory()->create([
            'subscription_expires_at' => '2024-01-01 00:00:00',
            'last_subscription_payment_at' => '2023-12-01 00:00:00'
        ]);

        $this->assertInstanceOf(\Carbon\Carbon::class, $vendor->subscription_expires_at);
        $this->assertInstanceOf(\Carbon\Carbon::class, $vendor->last_subscription_payment_at);
    }

    /** @test */
    public function it_sets_default_subscription_status_to_pending_payment()
    {
        $vendor = Vendor::factory()->create([
            'subscription_status' => null
        ]);

        $this->assertEquals('pending_payment', $vendor->subscription_status);
    }
}
