<div class="relative">
    <!-- Timeline Line -->
    <div class="absolute left-6 top-0 h-full w-0.5 bg-gray-200"></div>

    <!-- Timeline Steps -->
    <div class="space-y-8">
        @foreach($timeline as $step)
            <div class="relative flex items-start">
                <!-- Status Dot -->
                <div class="flex-shrink-0 w-12">
                    @if($step['completed'])
                        <div class="w-3 h-3 bg-green-500 rounded-full border-4 border-green-100"></div>
                    @else
                        <div class="w-3 h-3 bg-gray-300 rounded-full border-4 border-gray-100"></div>
                    @endif
                </div>

                <!-- Status Content -->
                <div class="flex-grow ml-4">
                    <div class="flex items-center">
                        <h4 class="text-base font-medium {{ $step['completed'] ? 'text-green-600' : 'text-gray-500' }}">
                            {{ $step['title'] }}
                        </h4>
                        @if($step['date'])
                            <span class="ml-2 text-sm text-gray-500">
                                {{ $step['date']->format('M d, Y g:i A') }}
                            </span>
                        @endif
                    </div>
                    <p class="mt-1 text-sm text-gray-600">{{ $step['description'] }}</p>
                </div>

                <!-- Status Icon -->
                <div class="flex-shrink-0 ml-4">
                    @if($step['completed'])
                        <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    @endif
                </div>
            </div>
        @endforeach
    </div>
</div>

<!-- Mobile Timeline -->
<div class="md:hidden mt-4">
    <div class="space-y-4">
        @foreach($timeline as $step)
            <div class="flex items-center {{ $step['completed'] ? 'text-green-600' : 'text-gray-500' }}">
                @if($step['completed'])
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                @else
                    <div class="w-5 h-5 mr-2 rounded-full border-2 {{ $step['completed'] ? 'border-green-600' : 'border-gray-300' }}"></div>
                @endif
                <div>
                    <p class="font-medium">{{ $step['title'] }}</p>
                    @if($step['date'])
                        <p class="text-sm text-gray-500">{{ $step['date']->format('M d, Y g:i A') }}</p>
                    @endif
                </div>
            </div>
        @endforeach
    </div>
</div>
