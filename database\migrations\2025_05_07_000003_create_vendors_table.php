<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('vendors', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('business_name');
            $table->string('address_line1');
            $table->string('city');
            $table->string('state');
            $table->string('contact_phone');
            $table->string('contact_email_business');
            $table->enum('delivery_zone_type', ['platform_delivery_zone', 'other_states_zone']);
            $table->enum('subscription_status', ['active', 'inactive', 'pending_payment', 'expired'])->default('pending_payment');
            $table->boolean('approved')->default(false);
            $table->timestamps();
        });
    }
    public function down(): void
    {
        Schema::dropIfExists('vendors');
    }
};
