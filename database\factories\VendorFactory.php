<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

class VendorFactory extends Factory
{
    protected $model = Vendor::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'business_name' => $this->faker->company(),
            'slug' => $this->faker->slug(),
            'business_registration_number' => $this->faker->numerify('RC-#######'),
            'tax_identification_number' => $this->faker->numerify('TIN-#########'),
            'bank_account_details' => [
                'bank_name' => $this->faker->company(),
                'account_number' => $this->faker->numerify('##########'),
                'account_name' => $this->faker->company()
            ],
            'address_line1' => $this->faker->streetAddress(),
            'address_line2' => $this->faker->secondaryAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->randomElement(['Lagos', 'Abuja', 'Ibadan', 'Akure', 'Oyo', 'Enugu']),
            'postal_code' => $this->faker->postcode(),
            'contact_phone' => $this->faker->numerify('234##########'),
            'contact_email_business' => $this->faker->companyEmail(),
            'delivery_zone_type' => null, // Will be set by model boot method
            'delivery_policy_text' => $this->faker->paragraph(),
            'subscription_status' => $this->faker->randomElement(['active', 'inactive', 'pending_payment', 'expired']),
            'subscription_expires_at' => $this->faker->dateTimeBetween('now', '+1 year'),
            'last_subscription_payment_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'is_approved' => $this->faker->boolean(),
        ];
    }

    /**
     * Configure the model factory to indicate the vendor is approved.
     */
    public function approved(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'is_approved' => true,
            ];
        });
    }

    /**
     * Configure the model factory to indicate the vendor has an active subscription.
     */
    public function activeSubscription(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'subscription_status' => 'active',
                'subscription_expires_at' => now()->addMonths(1),
                'last_subscription_payment_at' => now(),
            ];
        });
    }

    /**
     * Configure the model factory to be in platform delivery zone.
     */
    public function inPlatformZone(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'state' => $this->faker->randomElement(['Lagos', 'Abuja', 'Ibadan', 'Akure']),
            ];
        });
    }

    /**
     * Configure the model factory to be outside platform delivery zone.
     */
    public function outsidePlatformZone(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'state' => $this->faker->randomElement(['Oyo', 'Enugu', 'Kano', 'Port Harcourt']),
            ];
        });
    }
}
