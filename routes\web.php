<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\Vendor\DashboardController as VendorDashboardController;
use App\Http\Controllers\Vendor\ProductController as VendorProductController;
use App\Http\Controllers\Vendor\OrderController as VendorOrderController;
use App\Http\Controllers\Vendor\ProfileController as VendorProfileController;
use App\Http\Controllers\VendorController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Home Route
Route::get('/', [HomeController::class, 'index'])->name('home');

// Product Routes
Route::get('/products', [\App\Http\Controllers\ProductController::class, 'index'])->name('products.index');
Route::get('/products/{product}', [\App\Http\Controllers\ProductController::class, 'show'])->name('products.show');
Route::get('/category/{category}', [\App\Http\Controllers\ProductController::class, 'category'])->name('products.category');
Route::get('/vendors/{vendor:slug}', [\App\Http\Controllers\ProductController::class, 'vendor'])->name('vendors.storefront');

// Vendor Routes
Route::prefix('vendor')->group(function () {
    // Vendor Registration & Profile
    Route::get('/register', [\App\Http\Controllers\VendorController::class, 'register'])->name('vendor.register');
    Route::post('/register', [\App\Http\Controllers\VendorController::class, 'store']);
    
    // Vendor Onboarding (before approval)
    Route::get('/onboarding', [\App\Http\Controllers\VendorController::class, 'onboarding'])
    ->middleware('auth')
    ->name('vendor.onboarding');
    // Route for completeProfile removed as functionality is merged into vendor.profile.update
    
    // Authenticated Vendor Routes
    Route::middleware(['auth', 'vendor'])->group(function () {
    Route::get('/dashboard', [VendorDashboardController::class, 'index'])->name('vendor.dashboard');
    
    // Profile Management
    Route::get('/profile', [\App\Http\Controllers\VendorController::class, 'edit'])->name('vendor.profile.edit');
    Route::put('/profile', [\App\Http\Controllers\VendorController::class, 'update'])->name('vendor.profile.update');
    
    // Product Management
    Route::resource('products', VendorProductController::class);
    
    // Order Management
    Route::get('orders', [VendorOrderController::class, 'index'])->name('vendor.orders.index');
    Route::get('orders/{order}', [VendorOrderController::class, 'show'])->name('vendor.orders.show');
    Route::put('orders/{order}/status', [VendorOrderController::class, 'updateStatus'])->name('vendor.orders.updateStatus');
    
    // Earnings Management
    Route::get('/earnings', [\App\Http\Controllers\Vendor\EarningsController::class, 'index'])->name('vendor.earnings.index');
    Route::post('/earnings/withdraw', [\App\Http\Controllers\Vendor\EarningsController::class, 'withdraw'])->name('vendor.earnings.withdraw');
    
    // Subscription Management
    Route::get('/subscription', [\App\Http\Controllers\Vendor\SubscriptionController::class, 'index'])->name('vendor.subscription.index');
    Route::get('/pay-subscription', [\App\Http\Controllers\VendorController::class, 'paySubscription'])->name('vendor.subscription.pay');
    Route::get('/subscription-callback', [\App\Http\Controllers\VendorController::class, 'subscriptionCallback'])->name('vendor.subscription.callback');
    
    // Settings
    Route::get('/settings', [\App\Http\Controllers\Vendor\SettingsController::class, 'index'])->name('vendor.settings.index');
    Route::put('/settings', [\App\Http\Controllers\Vendor\SettingsController::class, 'update'])->name('vendor.settings.update');
    });
});

// Checkout Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/checkout', [\App\Http\Controllers\CheckoutController::class, 'index'])->name('checkout.index');
    Route::post('/checkout', [\App\Http\Controllers\CheckoutController::class, 'process'])->name('checkout.process');
    Route::get('/checkout/success', [\App\Http\Controllers\CheckoutController::class, 'success'])->name('checkout.success');
});

// Order Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/orders', [\App\Http\Controllers\OrderController::class, 'index'])->name('orders.index');
    Route::get('/orders/{order}', [\App\Http\Controllers\OrderController::class, 'show'])->name('orders.show');
    Route::get('/orders/{order}/pay', [\App\Http\Controllers\OrderController::class, 'pay'])->name('orders.pay');
    Route::get('/orders/{order}/paystack-callback', [\App\Http\Controllers\OrderController::class, 'paystackCallback'])->name('orders.paystack.callback');
    Route::post('/orders/{order}/cancel', [\App\Http\Controllers\OrderController::class, 'cancel'])->name('orders.cancel');
    Route::post('/orders/{order}/return', [\App\Http\Controllers\OrderController::class, 'requestReturn'])->name('orders.return');
});

// Cart Routes
Route::prefix('cart')->group(function () {
    Route::get('/', [\App\Http\Controllers\CartController::class, 'index'])->name('cart.index');
    Route::post('/add/{product}', [\App\Http\Controllers\CartController::class, 'add'])->name('cart.add');
    Route::put('/update/{id}', [\App\Http\Controllers\CartController::class, 'update'])->name('cart.update');
    Route::delete('/remove/{id}', [\App\Http\Controllers\CartController::class, 'remove'])->name('cart.remove');
    Route::delete('/clear', [\App\Http\Controllers\CartController::class, 'clear'])->name('cart.clear');
});

// Paystack Webhook
Route::post('/paystack/webhook', [\App\Http\Controllers\PaystackWebhookController::class, 'handle'])->name('paystack.webhook');

// ShipBubble Webhook
Route::post('/shipbubble/webhook', [\App\Http\Controllers\ShipBubbleWebhookController::class, 'handle'])->name('shipbubble.webhook');

// Shipment Tracking
Route::get('/track/{tracking_number}', [\App\Http\Controllers\TrackingController::class, 'show'])->name('shipments.track');

// Admin Routes
Route::prefix('admin')->middleware(['auth', 'admin'])->group(function () {
    Route::get('/dashboard', [\App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('admin.dashboard');
    Route::get('/analytics', [\App\Http\Controllers\Admin\AnalyticsController::class, 'index'])->name('admin.analytics');
    
    // Vendor Management
    Route::resource('vendors', \App\Http\Controllers\Admin\VendorController::class, ['as' => 'admin']);
    Route::post('/vendors/{vendor}/approve', [\App\Http\Controllers\Admin\VendorController::class, 'approve'])->name('admin.vendors.approve');
    Route::post('/vendors/{vendor}/reject', [\App\Http\Controllers\Admin\VendorController::class, 'reject'])->name('admin.vendors.reject');
    Route::post('/vendors/{vendor}/toggle-featured', [\App\Http\Controllers\Admin\VendorController::class, 'toggleFeatured'])->name('admin.vendors.toggle-featured');
    
    // Product Management
    Route::resource('products', \App\Http\Controllers\Admin\ProductController::class, ['as' => 'admin']);
    
    // Order Management
    Route::resource('orders', \App\Http\Controllers\Admin\OrderController::class, ['as' => 'admin']);
    
    // Commission Management
    Route::get('/commissions', [\App\Http\Controllers\Admin\CommissionController::class, 'index'])->name('admin.commissions.index');
    Route::get('/commissions/report', [\App\Http\Controllers\Admin\CommissionController::class, 'report'])->name('admin.commissions.report');
    
    // User Management
    Route::resource('users', \App\Http\Controllers\Admin\UserController::class, ['as' => 'admin']);
    
    // Category Management
    Route::resource('categories', \App\Http\Controllers\Admin\CategoryController::class, ['as' => 'admin']);
    
    // Brand Management
    Route::resource('brands', \App\Http\Controllers\Admin\BrandController::class, ['as' => 'admin']);
});

// General User Settings/Profile Routes (using Livewire)
Route::middleware(['auth'])->group(function () {
    Route::get('/settings/profile', \App\Livewire\Settings\Profile::class)->name('settings.profile');
    // Add other settings routes here if needed, e.g., for password, appearance
    // Route::get('/settings/password', \App\Livewire\Settings\Password::class)->name('settings.password');
    // Route::get('/settings/appearance', \App\Livewire\Settings\Appearance::class)->name('settings.appearance');
});

// Authentication Routes
require __DIR__.'/auth.php';
